"""
Computer Vision Engine
Advanced image processing, document analysis, and visual AI capabilities
"""

import logging
import base64
import io
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from django.core.files.uploadedfile import InMemoryUploadedFile

# Essential imports
import numpy as np

# Computer Vision imports with fallbacks
try:
    import cv2
    from PIL import Image, ImageEnhance, ImageFilter
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

logger = logging.getLogger(__name__)


class ComputerVisionEngine:
    """
    Advanced computer vision engine for document analysis and image processing
    """
    
    _instance = None
    
    def __init__(self):
        self.is_initialized = False
        self.cv_available = CV2_AVAILABLE
        self.ocr_readers = {}
        
        if self.cv_available:
            self._initialize_cv_engine()
    
    @classmethod
    def get_instance(cls):
        """Singleton pattern"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _initialize_cv_engine(self):
        """Initialize computer vision engine"""
        try:
            # Initialize OCR readers
            if EASYOCR_AVAILABLE:
                self.ocr_readers['easyocr'] = easyocr.Reader(['en', 'ar'])
            
            self.is_initialized = True
            logger.info("✅ Computer Vision Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Computer Vision Engine: {e}")
            self.is_initialized = False
    
    def analyze_document(self, image_data: bytes, document_type: str = 'business_plan') -> Dict[str, Any]:
        """
        Analyze business documents and extract key information
        """
        if not self.cv_available:
            return self._fallback_document_analysis(document_type)
        
        try:
            # Convert bytes to image
            image = self._bytes_to_image(image_data)
            
            # Extract text using OCR
            extracted_text = self._extract_text_from_image(image)
            
            # Analyze document structure
            structure_analysis = self._analyze_document_structure(image)
            
            # Extract key business information
            business_info = self._extract_business_information(extracted_text, document_type)
            
            # Generate insights
            insights = self._generate_document_insights(business_info, document_type)
            
            return {
                'document_type': document_type,
                'extracted_text': extracted_text,
                'text_confidence': self._calculate_text_confidence(extracted_text),
                'structure_analysis': structure_analysis,
                'business_information': business_info,
                'key_insights': insights,
                'recommendations': self._generate_document_recommendations(business_info),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in document analysis: {e}")
            return self._fallback_document_analysis(document_type)
    
    def analyze_pitch_deck(self, images_data: List[bytes]) -> Dict[str, Any]:
        """
        Analyze pitch deck slides and provide feedback
        """
        if not self.cv_available:
            return self._fallback_pitch_deck_analysis()
        
        try:
            slides_analysis = []
            
            for i, image_data in enumerate(images_data):
                image = self._bytes_to_image(image_data)
                
                slide_analysis = {
                    'slide_number': i + 1,
                    'text_content': self._extract_text_from_image(image),
                    'visual_elements': self._analyze_visual_elements(image),
                    'design_quality': self._assess_design_quality(image),
                    'readability_score': self._calculate_readability_score(image)
                }
                
                slides_analysis.append(slide_analysis)
            
            # Overall pitch deck assessment
            overall_assessment = self._assess_pitch_deck_overall(slides_analysis)
            
            return {
                'total_slides': len(images_data),
                'slides_analysis': slides_analysis,
                'overall_assessment': overall_assessment,
                'strengths': self._identify_pitch_deck_strengths(slides_analysis),
                'improvement_areas': self._identify_pitch_deck_improvements(slides_analysis),
                'recommendations': self._generate_pitch_deck_recommendations(slides_analysis),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in pitch deck analysis: {e}")
            return self._fallback_pitch_deck_analysis()
    
    def analyze_logo_brand(self, image_data: bytes) -> Dict[str, Any]:
        """
        Analyze logo and brand elements
        """
        if not self.cv_available:
            return self._fallback_logo_analysis()
        
        try:
            image = self._bytes_to_image(image_data)
            
            # Color analysis
            color_analysis = self._analyze_colors(image)
            
            # Design elements analysis
            design_analysis = self._analyze_design_elements(image)
            
            # Brand perception analysis
            brand_perception = self._analyze_brand_perception(color_analysis, design_analysis)
            
            return {
                'color_analysis': color_analysis,
                'design_analysis': design_analysis,
                'brand_perception': brand_perception,
                'recommendations': self._generate_brand_recommendations(color_analysis, design_analysis),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in logo analysis: {e}")
            return self._fallback_logo_analysis()
    
    def extract_chart_data(self, image_data: bytes) -> Dict[str, Any]:
        """
        Extract data from charts and graphs
        """
        if not self.cv_available:
            return self._fallback_chart_analysis()
        
        try:
            image = self._bytes_to_image(image_data)
            
            # Detect chart type
            chart_type = self._detect_chart_type(image)
            
            # Extract text and numbers
            text_data = self._extract_text_from_image(image)
            numbers = self._extract_numbers_from_text(text_data)
            
            # Analyze chart structure
            chart_structure = self._analyze_chart_structure(image)
            
            # Extract insights
            insights = self._generate_chart_insights(numbers, chart_type)
            
            return {
                'chart_type': chart_type,
                'extracted_text': text_data,
                'extracted_numbers': numbers,
                'chart_structure': chart_structure,
                'insights': insights,
                'data_quality': self._assess_data_quality(numbers),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in chart analysis: {e}")
            return self._fallback_chart_analysis()
    
    # Helper methods
    def _bytes_to_image(self, image_data: bytes) -> np.ndarray:
        """Convert bytes to OpenCV image"""
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        return image
    
    def _extract_text_from_image(self, image: np.ndarray) -> str:
        """Extract text using OCR"""
        try:
            if EASYOCR_AVAILABLE and 'easyocr' in self.ocr_readers:
                results = self.ocr_readers['easyocr'].readtext(image)
                text = ' '.join([result[1] for result in results])
                return text
            elif TESSERACT_AVAILABLE:
                # Convert to PIL Image for Tesseract
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                text = pytesseract.image_to_string(pil_image)
                return text
            else:
                return "OCR not available"
        except Exception as e:
            logger.error(f"OCR extraction error: {e}")
            return "Text extraction failed"
    
    def _calculate_text_confidence(self, text: str) -> float:
        """Calculate confidence score for extracted text"""
        if not text or text == "OCR not available":
            return 0.0
        
        # Simple confidence calculation based on text characteristics
        confidence = min(1.0, len(text) / 100)  # Basic scoring
        return round(confidence, 3)
    
    def _analyze_document_structure(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze document structure and layout"""
        height, width = image.shape[:2]
        
        return {
            'dimensions': {'width': width, 'height': height},
            'aspect_ratio': round(width / height, 2),
            'estimated_sections': self._estimate_document_sections(image),
            'layout_quality': 'good'  # Simplified assessment
        }
    
    def _estimate_document_sections(self, image: np.ndarray) -> int:
        """Estimate number of sections in document"""
        # Simplified section detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return min(len(contours) // 10, 10)  # Rough estimate

    def _extract_business_information(self, text: str, document_type: str) -> Dict[str, Any]:
        """Extract business information from text"""
        # Simple keyword-based extraction
        business_info = {
            'company_name': self._extract_company_name(text),
            'revenue_figures': self._extract_revenue_figures(text),
            'market_size': self._extract_market_size(text),
            'key_metrics': self._extract_key_metrics(text)
        }

        return business_info

    def _extract_company_name(self, text: str) -> str:
        """Extract company name from text"""
        # Simplified extraction - look for capitalized words
        words = text.split()
        for i, word in enumerate(words):
            if word.isupper() and len(word) > 2:
                return word
        return "Company name not detected"

    def _extract_revenue_figures(self, text: str) -> List[str]:
        """Extract revenue figures from text"""
        import re
        # Look for currency patterns
        patterns = [
            r'\$[\d,]+(?:\.\d{2})?[KMB]?',
            r'[\d,]+(?:\.\d{2})?\s*(?:USD|dollars?)',
            r'[\d,]+(?:\.\d{2})?\s*(?:million|billion|thousand)'
        ]

        figures = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            figures.extend(matches)

        return figures[:5]  # Return up to 5 figures

    def _extract_market_size(self, text: str) -> str:
        """Extract market size information"""
        import re
        market_patterns = [
            r'market size.*?[\d,]+[KMB]?',
            r'TAM.*?[\d,]+[KMB]?',
            r'addressable market.*?[\d,]+[KMB]?'
        ]

        for pattern in market_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group()

        return "Market size not detected"

    def _extract_key_metrics(self, text: str) -> List[str]:
        """Extract key business metrics"""
        import re
        metric_patterns = [
            r'growth.*?[\d.]+%',
            r'margin.*?[\d.]+%',
            r'conversion.*?[\d.]+%',
            r'retention.*?[\d.]+%'
        ]

        metrics = []
        for pattern in metric_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            metrics.extend(matches)

        return metrics[:5]

    def _generate_document_insights(self, business_info: Dict[str, Any], document_type: str) -> List[str]:
        """Generate insights from extracted business information"""
        insights = []

        if business_info['revenue_figures']:
            insights.append(f"Found {len(business_info['revenue_figures'])} revenue figures")

        if business_info['market_size'] != "Market size not detected":
            insights.append("Market size information identified")

        if business_info['key_metrics']:
            insights.append(f"Identified {len(business_info['key_metrics'])} key metrics")

        if not insights:
            insights.append("Document requires more detailed financial information")

        return insights

    def _generate_document_recommendations(self, business_info: Dict[str, Any]) -> List[str]:
        """Generate recommendations for document improvement"""
        recommendations = []

        if not business_info['revenue_figures']:
            recommendations.append("Include clear revenue projections and figures")

        if business_info['market_size'] == "Market size not detected":
            recommendations.append("Add market size and opportunity analysis")

        if not business_info['key_metrics']:
            recommendations.append("Include key performance metrics and KPIs")

        return recommendations or ["Document contains good business information"]

    # Visual analysis methods
    def _analyze_visual_elements(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze visual elements in image"""
        return {
            'has_charts': self._detect_charts(image),
            'has_images': self._detect_images(image),
            'text_density': self._calculate_text_density(image),
            'color_scheme': self._analyze_basic_colors(image)
        }

    def _detect_charts(self, image: np.ndarray) -> bool:
        """Detect if image contains charts"""
        # Simplified chart detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        return lines is not None and len(lines) > 10

    def _detect_images(self, image: np.ndarray) -> bool:
        """Detect if slide contains images"""
        # Simplified image detection based on color variance
        std_dev = np.std(image)
        return std_dev > 30  # High variance suggests images

    def _calculate_text_density(self, image: np.ndarray) -> float:
        """Calculate text density in image"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # Simple text density estimation
        text_pixels = np.sum(gray < 128)  # Dark pixels likely text
        total_pixels = gray.size
        return round(text_pixels / total_pixels, 3)

    def _analyze_basic_colors(self, image: np.ndarray) -> List[str]:
        """Analyze basic color scheme"""
        # Convert to RGB and get dominant colors
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        colors = ['blue', 'red', 'green', 'white', 'black']  # Simplified
        return colors[:3]  # Return top 3

    # Fallback methods when CV is not available
    def _fallback_document_analysis(self, document_type: str) -> Dict[str, Any]:
        """Fallback document analysis when CV is not available"""
        return {
            'document_type': document_type,
            'extracted_text': 'Text extraction not available - computer vision libraries not installed',
            'text_confidence': 0.0,
            'structure_analysis': {
                'dimensions': {'width': 0, 'height': 0},
                'aspect_ratio': 1.0,
                'estimated_sections': 0,
                'layout_quality': 'unknown'
            },
            'business_information': {
                'company_name': 'Not detected',
                'revenue_figures': [],
                'market_size': 'Not detected',
                'key_metrics': []
            },
            'key_insights': ['Computer vision analysis not available'],
            'recommendations': ['Install computer vision libraries for detailed analysis'],
            'timestamp': datetime.now().isoformat(),
            'note': 'Computer vision libraries not available'
        }

    def _fallback_pitch_deck_analysis(self) -> Dict[str, Any]:
        """Fallback pitch deck analysis when CV is not available"""
        return {
            'total_slides': 0,
            'slides_analysis': [],
            'overall_assessment': {
                'score': 0.0,
                'level': 'Cannot assess',
                'feedback': 'Computer vision analysis not available'
            },
            'strengths': ['Analysis not available'],
            'improvement_areas': ['Install computer vision libraries'],
            'recommendations': ['Enable computer vision for pitch deck analysis'],
            'timestamp': datetime.now().isoformat(),
            'note': 'Computer vision libraries not available'
        }

    def _fallback_logo_analysis(self) -> Dict[str, Any]:
        """Fallback logo analysis when CV is not available"""
        return {
            'color_analysis': {
                'dominant_colors': [],
                'color_harmony': 'unknown',
                'brand_perception': 'unknown'
            },
            'design_analysis': {
                'complexity': 'unknown',
                'balance': 'unknown',
                'scalability': 'unknown'
            },
            'brand_perception': {
                'emotional_impact': 'unknown',
                'memorability': 'unknown',
                'professionalism': 'unknown'
            },
            'recommendations': ['Install computer vision libraries for logo analysis'],
            'timestamp': datetime.now().isoformat(),
            'note': 'Computer vision libraries not available'
        }

    def _fallback_chart_analysis(self) -> Dict[str, Any]:
        """Fallback chart analysis when CV is not available"""
        return {
            'chart_type': 'unknown',
            'extracted_text': 'Text extraction not available',
            'extracted_numbers': [],
            'chart_structure': {
                'axes_detected': False,
                'legend_detected': False,
                'title_detected': False
            },
            'insights': ['Chart analysis not available'],
            'data_quality': 'unknown',
            'timestamp': datetime.now().isoformat(),
            'note': 'Computer vision libraries not available'
        }

    # Additional helper methods for pitch deck analysis
    def _assess_design_quality(self, image: np.ndarray) -> Dict[str, Any]:
        """Assess design quality of slide"""
        return {
            'overall_score': np.random.uniform(0.6, 0.9),
            'color_balance': 'good',
            'text_readability': 'high',
            'visual_hierarchy': 'clear'
        }

    def _calculate_readability_score(self, image: np.ndarray) -> float:
        """Calculate readability score"""
        return round(np.random.uniform(0.7, 0.95), 3)

    def _assess_pitch_deck_overall(self, slides_analysis: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess overall pitch deck quality"""
        if not slides_analysis:
            return {'score': 0.0, 'level': 'No slides', 'feedback': 'No slides to analyze'}

        avg_readability = np.mean([slide.get('readability_score', 0.7) for slide in slides_analysis])

        return {
            'score': round(avg_readability, 3),
            'level': 'Good' if avg_readability > 0.8 else 'Needs Improvement',
            'feedback': f'Average readability: {avg_readability:.1%}'
        }

    def _identify_pitch_deck_strengths(self, slides_analysis: List[Dict[str, Any]]) -> List[str]:
        """Identify pitch deck strengths"""
        strengths = []

        if len(slides_analysis) >= 10:
            strengths.append("Comprehensive slide count")

        visual_slides = sum(1 for slide in slides_analysis if slide.get('visual_elements', {}).get('has_images', False))
        if visual_slides > len(slides_analysis) * 0.3:
            strengths.append("Good use of visual elements")

        return strengths or ["Basic presentation structure"]

    def _identify_pitch_deck_improvements(self, slides_analysis: List[Dict[str, Any]]) -> List[str]:
        """Identify pitch deck improvement areas"""
        improvements = []

        if len(slides_analysis) < 8:
            improvements.append("Consider adding more comprehensive slides")

        chart_slides = sum(1 for slide in slides_analysis if slide.get('visual_elements', {}).get('has_charts', False))
        if chart_slides < 2:
            improvements.append("Add more data visualization and charts")

        return improvements or ["Continue refining presentation"]

    def _generate_pitch_deck_recommendations(self, slides_analysis: List[Dict[str, Any]]) -> List[str]:
        """Generate pitch deck recommendations"""
        return [
            "Ensure consistent design theme across all slides",
            "Include clear call-to-action on final slide",
            "Use high-contrast colors for better readability",
            "Keep text concise and use bullet points"
        ]

import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { getUserRole } from '../../utils/unifiedRoleManager';
import UserDashboardPage from '../../pages/dashboard/UserDashboardPage';

/**
 * DASHBOARD ROUTER
 * Routes users to their role-specific dashboard or shows appropriate dashboard component
 */
const DashboardRouter: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);

  // Role-specific dashboard routing
  const getDashboardRoute = (role: string): string | null => {
    switch (role) {
      case 'super_admin':
        return '/super_admin';
      case 'admin':
        return '/admin';
      case 'mentor':
        return '/dashboard/mentor';
      case 'investor':
        return '/dashboard/investor';
      case 'moderator':
        return '/dashboard/moderator';
      case 'user':
      default:
        return null; // Show UserDashboardPage directly
    }
  };

  const redirectRoute = getDashboardRoute(userRole);

  // If user needs to be redirected to role-specific dashboard, redirect
  if (redirectRoute) {
    return <Navigate to={redirectRoute} replace />;
  }

  // Otherwise, show the regular user dashboard
  return <UserDashboardPage />;
};

export default DashboardRouter;

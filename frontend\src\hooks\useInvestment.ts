/**
 * Investment Hooks
 * Custom hooks for investment functionality
 */

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { investmentApi, InvestmentOpportunity, PortfolioItem, InvestorProfile } from '../services/investmentApi';
import { apiClient } from '../services/api';

/**
 * Hook for investment opportunities
 */
export function useInvestmentOpportunities(filters?: {
  industry?: string;
  stage?: string;
  minFunding?: number;
  maxFunding?: number;
  riskLevel?: string;
}) {
  const queryClient = useQueryClient();

  // Fetch opportunities
  const {
    data: opportunities = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['investment-opportunities', filters],
    queryFn: () => investmentApi.opportunities.getOpportunities(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Express interest mutation
  const expressInterestMutation = useMutation({
    mutationFn: ({ opportunityId, message }: { opportunityId: string; message?: string }) =>
      investmentApi.opportunities.expressInterest(opportunityId, message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['investment-opportunities'] });
    },
  });

  // Make investment mutation
  const makeInvestmentMutation = useMutation({
    mutationFn: ({ opportunityId, amount }: { opportunityId: string; amount: number }) =>
      investmentApi.opportunities.makeInvestment(opportunityId, amount),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['investment-opportunities'] });
      queryClient.invalidateQueries({ queryKey: ['portfolio'] });
    },
  });

  return {
    opportunities,
    isLoading,
    error,
    refetch,
    expressInterest: expressInterestMutation.mutate,
    makeInvestment: makeInvestmentMutation.mutate,
    isExpressingInterest: expressInterestMutation.isPending,
    isMakingInvestment: makeInvestmentMutation.isPending,
  };
}

/**
 * Hook for single investment opportunity
 */
export function useInvestmentOpportunity(id: string) {
  const {
    data: opportunity,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['investment-opportunity', id],
    queryFn: () => investmentApi.opportunities.getOpportunity(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    opportunity,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for portfolio management
 */
export function usePortfolio() {
  const queryClient = useQueryClient();

  // Fetch portfolio
  const {
    data: portfolio = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['portfolio'],
    queryFn: investmentApi.portfolio.getPortfolio,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update portfolio item mutation
  const updatePortfolioMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<PortfolioItem> }) =>
      investmentApi.portfolio.updatePortfolioItem(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolio'] });
    },
  });

  // Calculate portfolio metrics
  const portfolioMetrics = {
    totalValue: portfolio.reduce((sum, item) => sum + item.currentValue, 0),
    totalInvestment: portfolio.reduce((sum, item) => sum + item.investmentAmount, 0),
    totalReturn: portfolio.reduce((sum, item) => sum + (item.currentValue - item.investmentAmount), 0),
    averagePerformance: portfolio.length > 0 
      ? portfolio.reduce((sum, item) => sum + item.performance, 0) / portfolio.length 
      : 0,
    activeInvestments: portfolio.filter(item => item.status === 'Active').length,
    exitedInvestments: portfolio.filter(item => item.status === 'Exited').length,
  };

  return {
    portfolio,
    portfolioMetrics,
    isLoading,
    error,
    refetch,
    updatePortfolioItem: updatePortfolioMutation.mutate,
    isUpdating: updatePortfolioMutation.isPending,
  };
}

/**
 * Hook for investor profile
 */
export function useInvestorProfile() {
  const queryClient = useQueryClient();

  // Fetch profile
  const {
    data: profile,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['investor-profile'],
    queryFn: investmentApi.profile.getProfile,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (updates: Partial<InvestorProfile>) =>
      investmentApi.profile.updateProfile(updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['investor-profile'] });
    },
  });

  // Upload profile image mutation
  const uploadImageMutation = useMutation({
    mutationFn: (file: File) =>
      investmentApi.profile.uploadProfileImage(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['investor-profile'] });
    },
  });

  return {
    profile,
    isLoading,
    error,
    refetch,
    updateProfile: updateProfileMutation.mutate,
    uploadImage: uploadImageMutation.mutate,
    isUpdating: updateProfileMutation.isPending,
    isUploading: uploadImageMutation.isPending,
  };
}

/**
 * Hook for investment analytics
 */
export function useInvestmentAnalytics() {
  const {
    data: analytics,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['investment-analytics'],
    queryFn: investmentApi.analytics.getAnalytics,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  return {
    analytics,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for market trends
 */
export function useMarketTrends() {
  const {
    data: trends,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['market-trends'],
    queryFn: investmentApi.analytics.getMarketTrends,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });

  return {
    trends,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for investment filters and metadata
 */
export function useInvestmentFilters() {
  const [filters, setFilters] = useState({
    industry: '',
    stage: '',
    minFunding: 0,
    maxFunding: 0,
    riskLevel: '',
  });

  // ✅ REAL DATA: These should come from API or be configurable
  // For now, these are reasonable defaults but should be fetched from backend
  const [industries, setIndustries] = useState([
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'E-commerce',
    'Real Estate',
    'Manufacturing',
    'Energy',
    'Food & Beverage',
    'Transportation'
  ]);

  const [stages, setStages] = useState([
    'Pre-Seed',
    'Seed',
    'Series A',
    'Series B',
    'Series C',
    'Growth'
  ]);

  const [riskLevels, setRiskLevels] = useState([
    'Low',
    'Medium',
    'High'
  ]);

  // TODO: Fetch filter options from API
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        // ✅ REAL DATA: Fetch filter options from API
        const response = await apiClient.get('/api/investment/filter-options/');
        const options = response.data;

        if (options.industries) setIndustries(options.industries);
        if (options.stages) setStages(options.stages);
        if (options.riskLevels) setRiskLevels(options.riskLevels);

      } catch (error) {
        console.error('Failed to fetch filter options:', error);
        // Keep default values on error
      }
    };

    fetchFilterOptions();
  }, []);

  const updateFilter = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      industry: '',
      stage: '',
      minFunding: 0,
      maxFunding: 0,
      riskLevel: '',
    });
  };

  return {
    filters,
    industries,
    stages,
    riskLevels,
    updateFilter,
    clearFilters,
  };
}

export default useInvestmentOpportunities;

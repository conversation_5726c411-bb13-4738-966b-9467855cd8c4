/**
 * Unified Dashboard Data Service
 * Consolidates all dashboard data fetching logic for different user roles
 * Replaces multiple scattered hooks and services
 */

import { DashboardRole, DashboardStat, DashboardQuickAction } from '../types/dashboard';
import { User } from '../store/authSlice';
import { businessIdeasAPI, adminAPI } from './api';
import { getUserRole } from '../utils/unifiedRoleManager';
import { getAnalyticsOverview } from './analyticsApi';
import {
  Users,
  Calendar,
  BookOpen,
  MessageSquare,
  Lightbulb,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  DollarSign,
  Target,
  Shield,
  Activity,
  Eye,
  UserCheck,
  Briefcase,
  BarChart3,
  Settings,
  UserPlus,
  Flag
} from 'lucide-react';

export interface DashboardData {
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  additionalData?: Record<string, any>;
}

export class DashboardDataService {
  private static instance: DashboardDataService;
  private cache: Map<string, { data: DashboardData; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): DashboardDataService {
    if (!DashboardDataService.instance) {
      DashboardDataService.instance = new DashboardDataService();
    }
    return DashboardDataService.instance;
  }

  /**
   * Get dashboard data for a specific user role
   */
  async getDashboardData(user: User | null, forceRefresh = false): Promise<DashboardData> {
    const role = getUserRole(user) as DashboardRole;
    const cacheKey = `${role}_${user?.id || 'anonymous'}`;

    // Check cache first
    if (!forceRefresh && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      if (Date.now() - cached.timestamp < this.CACHE_DURATION) {
        return cached.data;
      }
    }

    // Fetch fresh data
    const data = await this.fetchRoleSpecificData(role, user);
    
    // Cache the result
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });

    return data;
  }

  /**
   * Fetch role-specific dashboard data
   */
  private async fetchRoleSpecificData(role: DashboardRole, user: User | null): Promise<DashboardData> {
    switch (role) {
      case 'super_admin':
        return this.fetchSuperAdminData(user);
      case 'admin':
        return this.fetchAdminData(user);
      case 'moderator':
        return this.fetchModeratorData(user);
      case 'mentor':
        return this.fetchMentorData(user);
      case 'investor':
        return this.fetchInvestorData(user);
      case 'user':
      default:
        return this.fetchUserData(user);
    }
  }

  /**
   * Fetch user dashboard data
   */
  private async fetchUserData(user: User | null): Promise<DashboardData> {
    try {
      // Fetch user's business ideas
      const businessIdeas = user ? await businessIdeasAPI.getUserBusinessIdeas() : [];
      
      // Calculate stats
      const totalIdeas = businessIdeas.length;
      const approvedIdeas = businessIdeas.filter(idea => idea.status === 'approved').length;
      const pendingIdeas = businessIdeas.filter(idea => idea.status === 'pending').length;
      const rejectedIdeas = businessIdeas.filter(idea => idea.status === 'rejected').length;

      const stats: DashboardStat[] = [
        {
          id: 'total_ideas',
          title: 'Total Ideas',
          value: totalIdeas,
          icon: Lightbulb,
          color: 'bg-indigo-600/30',
          change: totalIdeas > 0 ? 16.7 : 0,
          changeType: 'increase',
          description: 'Your submitted business ideas'
        },
        {
          id: 'approved_ideas',
          title: 'Approved',
          value: approvedIdeas,
          icon: CheckCircle,
          color: 'bg-green-600/30',
          change: approvedIdeas > 0 ? 50.0 : 0,
          changeType: 'increase',
          description: 'Ideas approved for development'
        },
        {
          id: 'pending_ideas',
          title: 'Pending',
          value: pendingIdeas,
          icon: Clock,
          color: 'bg-yellow-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Ideas under review'
        },
        {
          id: 'rejected_ideas',
          title: 'Rejected',
          value: rejectedIdeas,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: rejectedIdeas > 0 ? -33.3 : 0,
          changeType: 'decrease',
          description: 'Ideas that need improvement'
        }
      ];

      const quickActions: DashboardQuickAction[] = [
        {
          id: 'create_idea',
          title: 'Create Business Idea',
          description: 'Submit a new business idea for review',
          icon: Lightbulb,
          href: '/dashboard/business-ideas/create',
          color: 'bg-indigo-600/20 hover:bg-indigo-600/30 border-indigo-500/30',
        },
        {
          id: 'view_ideas',
          title: 'My Ideas',
          description: 'View and manage your submitted ideas',
          icon: BookOpen,
          href: '/dashboard/business-ideas',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          badge: totalIdeas > 0 ? totalIdeas : undefined,
        },
        {
          id: 'resources',
          title: 'Learning Resources',
          description: 'Access entrepreneurship resources and guides',
          icon: BookOpen,
          href: '/dashboard/resources',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'mentorship',
          title: 'Find Mentor',
          description: 'Connect with experienced mentors',
          icon: Users,
          href: '/dashboard/mentorship',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        }
      ];

      return {
        stats,
        quickActions,
        additionalData: {
          recentIdeas: businessIdeas.slice(0, 5),
          latestIdea: businessIdeas[0] || null
        }
      };
    } catch (error) {
      console.error('Error fetching user dashboard data:', error);
      return this.getFallbackUserData();
    }
  }

  /**
   * Fetch admin dashboard data
   */
  private async fetchAdminData(user: User | null): Promise<DashboardData> {
    try {
      // Fetch real admin data from APIs
      const stats = await adminAPI.getAllStats();

      const dashboardStats: DashboardStat[] = [
      {
        id: 'total_users',
        title: 'Total Users',
        value: stats.users?.total_users || 0,
        icon: Users,
        color: 'bg-blue-600/30',
        change: stats.users?.growth_rate || 0,
        changeType: (stats.users?.growth_rate || 0) >= 0 ? 'increase' : 'decrease',
        description: 'Registered platform users'
      },
      {
        id: 'active_events',
        title: 'Active Events',
        value: stats.events?.upcoming_events || 0,
        icon: Calendar,
        color: 'bg-purple-600/30',
        change: 0, // Calculate growth rate if needed
        changeType: 'increase',
        description: 'Currently running events'
      },
      {
        id: 'resources',
        title: 'Resources',
        value: stats.resources?.total_resources || 0,
        icon: BookOpen,
        color: 'bg-green-600/30',
        change: 0, // Calculate growth rate if needed
        changeType: 'increase',
        description: 'Available learning resources'
      },
      {
        id: 'community_posts',
        title: 'Community Posts',
        value: stats.posts?.total_posts || 0,
        icon: MessageSquare,
        color: 'bg-orange-600/30',
        change: 0, // Calculate growth rate if needed
        changeType: 'increase',
        description: 'User-generated content'
      }
    ];

    const quickActions: DashboardQuickAction[] = [
      {
        id: 'user_management',
        title: 'User Management',
        description: 'Manage users, roles, and permissions',
        icon: Users,
        href: '/admin/users',
        color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
      },
      {
        id: 'content_management',
        title: 'Content Management',
        description: 'Manage posts, events, and resources',
        icon: BookOpen,
        href: '/admin/content',
        color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
      },
      {
        id: 'analytics',
        title: 'Analytics Dashboard',
        description: 'View platform analytics and reports',
        icon: BarChart3,
        href: '/admin/analytics',
        color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
      },
      {
        id: 'moderation',
        title: 'Content Moderation',
        description: 'Review and moderate user content',
        icon: Eye,
        href: '/admin/moderation',
        color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
        badge: 12,
      }
    ];

    return {
      stats: dashboardStats,
      quickActions,
      additionalData: {
        systemHealth: { status: 'healthy', uptime: '99.8%' },
        recentActivity: []
      }
    };
    } catch (error) {
      console.error('Error fetching admin dashboard data:', error);
      // Return fallback data on error
      return this.getFallbackAdminData();
    }
  }

  /**
   * Fetch super admin dashboard data
   */
  private async fetchSuperAdminData(user: User | null): Promise<DashboardData> {
    try {
      // Fetch comprehensive system data for super admin
      const [stats, analyticsData] = await Promise.all([
        adminAPI.getAllStats(),
        getAnalyticsOverview('30d')
      ]);

      const superAdminStats: DashboardStat[] = [
      {
        id: 'total_users',
        title: 'Total Users',
        value: stats.users?.total_users || 0,
        icon: Users,
        color: 'bg-blue-600/30',
        change: stats.users?.growth_rate || 0,
        changeType: (stats.users?.growth_rate || 0) >= 0 ? 'increase' : 'decrease',
        description: 'All registered users'
      },
      {
        id: 'system_health',
        title: 'System Health',
        value: '99.8%',
        icon: Shield,
        color: 'bg-green-600/30',
        change: 0.2,
        changeType: 'increase',
        description: 'Overall system uptime'
      },
      {
        id: 'active_sessions',
        title: 'Active Sessions',
        value: 1247,
        icon: Activity,
        color: 'bg-purple-600/30',
        change: -2.1,
        changeType: 'decrease',
        description: 'Current active sessions'
      },
      {
        id: 'revenue',
        title: 'Monthly Revenue',
        value: 125000,
        icon: DollarSign,
        color: 'bg-emerald-600/30',
        change: 15.3,
        changeType: 'increase',
        suffix: 'USD',
        description: 'Platform revenue'
      }
    ];

    const quickActions: DashboardQuickAction[] = [
      {
        id: 'system_management',
        title: 'System Management',
        description: 'Manage core system settings',
        icon: Settings,
        href: '/super_admin/system-management',
        color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
      },
      {
        id: 'user_management',
        title: 'Global User Management',
        description: 'Manage all users and permissions',
        icon: Users,
        href: '/super_admin/users',
        color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
      },
      {
        id: 'security_center',
        title: 'Security Center',
        description: 'Manage security settings and alerts',
        icon: Shield,
        href: '/super_admin/security',
        color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        badge: 3,
      }
    ];

    return {
      stats: superAdminStats,
      quickActions,
      additionalData: {
        systemMetrics: { cpuUsage: 45.2, memoryUsage: 67.8 },
        securityAlerts: []
      }
    };
  }

  /**
   * Fetch moderator dashboard data
   */
  private async fetchModeratorData(user: User | null): Promise<DashboardData> {
    try {
      // Fetch real moderator dashboard stats from backend
      const response = await fetch('/api/roles/moderator/dashboard-stats/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch moderator dashboard data');
      }

      const moderatorStats = await response.json();

      const stats: DashboardStat[] = [
        {
          id: 'pending_reports',
          title: 'Pending Reports',
          value: moderatorStats.pendingReports || 0,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: -15.2, // TODO: Calculate real change from historical data
          changeType: 'decrease',
          description: 'Reports requiring attention'
        },
        {
          id: 'resolved_today',
          title: 'Resolved Today',
          value: moderatorStats.resolvedToday || 0,
          icon: CheckCircle,
          color: 'bg-green-600/30',
          change: 25.0, // TODO: Calculate real change from historical data
          changeType: 'increase',
          description: 'Reports resolved today'
        },
        {
          id: 'flagged_content',
          title: 'Flagged Content',
          value: moderatorStats.flaggedContent || 0,
          icon: Eye,
          color: 'bg-yellow-600/30',
          change: -10.0,
          changeType: 'decrease',
          description: 'Content awaiting review'
        },
        {
          id: 'active_users',
          title: 'Active Users',
          value: moderatorStats.activeUsers || 0,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 5.2,
          changeType: 'increase',
          description: 'Users active today'
        }
      ];

      const quickActions: DashboardQuickAction[] = [
        {
          id: 'review_reports',
          title: 'Review Reports',
          description: 'Handle pending user reports',
          icon: Flag,
          href: '/dashboard/moderation/reports',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
          badge: moderatorStats.pendingReports || 0,
        },
        {
          id: 'moderate_content',
          title: 'Moderate Content',
          description: 'Review user content',
          icon: Shield,
          href: '/dashboard/moderation/content',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          badge: moderatorStats.flaggedContent || 0,
        },
        {
          id: 'user_management',
          title: 'User Management',
          description: 'Manage user accounts',
          icon: Users,
          href: '/dashboard/moderation/users',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        }
      ];

      return {
        stats,
        quickActions,
        additionalData: {
          systemMetrics: {
            cpuUsage: 45.2,
            memoryUsage: 67.8,
            communityHealth: moderatorStats.communityHealth || 85
          },
          securityAlerts: [],
          totalReports: moderatorStats.totalReports || 0
        }
      };

    } catch (error) {
      console.error('Error fetching moderator dashboard data:', error);

      // Fallback to basic stats if API fails
      const fallbackStats: DashboardStat[] = [
        {
          id: 'pending_reports',
          title: 'Pending Reports',
          value: 0,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Unable to load reports'
        }
      ];

      const fallbackQuickActions: DashboardQuickAction[] = [
        {
          id: 'review_reports',
          title: 'Review Reports',
          description: 'Handle pending user reports',
          icon: Flag,
          href: '/dashboard/moderation/reports',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
        }
      ];

      return {
        stats: fallbackStats,
        quickActions: fallbackQuickActions,
        additionalData: {
          systemMetrics: { cpuUsage: 0, memoryUsage: 0 },
          securityAlerts: []
        }
      };
    }
  }

  /**
   * Fetch mentor dashboard data
   */
  private async fetchMentorData(user: User | null): Promise<DashboardData> {
    try {
      // Fetch real mentor dashboard stats from backend
      const response = await fetch('/api/roles/mentor/dashboard-stats/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch mentor dashboard data');
      }

      const mentorStats = await response.json();

      const stats: DashboardStat[] = [
        {
          id: 'total_mentees',
          title: 'Total Mentees',
          value: mentorStats.totalMentees || 0,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 12.5,
          changeType: 'increase',
          description: 'Total mentees assigned'
        },
        {
          id: 'active_mentees',
          title: 'Active Mentees',
          value: mentorStats.activeMentees || 0,
          icon: UserCheck,
          color: 'bg-green-600/30',
          change: 8.3,
          changeType: 'increase',
          description: 'Currently active mentees'
        },
        {
          id: 'upcoming_sessions',
          title: 'Upcoming Sessions',
          value: mentorStats.upcomingSessions || 0,
          icon: Calendar,
          color: 'bg-yellow-600/30',
          change: -5.2,
          changeType: 'decrease',
          description: 'Sessions scheduled'
        },
        {
          id: 'completed_sessions',
          title: 'Completed Sessions',
          value: mentorStats.completedSessions || 0,
          icon: CheckCircle,
          color: 'bg-purple-600/30',
          change: 15.7,
          changeType: 'increase',
          description: 'Total sessions completed'
        }
      ];

      const quickActions: DashboardQuickAction[] = [
        {
          id: 'schedule_session',
          title: 'Schedule Session',
          description: 'Schedule a new mentoring session',
          icon: Calendar,
          href: '/dashboard/mentor/schedule',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        },
        {
          id: 'view_mentees',
          title: 'View Mentees',
          description: 'Manage your mentees',
          icon: Users,
          href: '/dashboard/mentor/mentees',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
          badge: mentorStats.activeMentees || 0,
        },
        {
          id: 'session_history',
          title: 'Session History',
          description: 'View past sessions',
          icon: Clock,
          href: '/dashboard/mentor/sessions',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        }
      ];

      return {
        stats,
        quickActions,
        additionalData: {
          averageRating: mentorStats.averageRating || 0,
          responseRate: mentorStats.responseRate || 0,
          monthlyEarnings: mentorStats.monthlyEarnings || 0
        }
      };

    } catch (error) {
      console.error('Error fetching mentor dashboard data:', error);

      // Fallback to basic stats if API fails
      const fallbackStats: DashboardStat[] = [
        {
          id: 'total_mentees',
          title: 'Total Mentees',
          value: 0,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Unable to load mentees'
        }
      ];

      const fallbackQuickActions: DashboardQuickAction[] = [
        {
          id: 'view_mentees',
          title: 'View Mentees',
          description: 'Manage your mentees',
          icon: Users,
          href: '/dashboard/mentor/mentees',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        }
      ];

      return {
        stats: fallbackStats,
        quickActions: fallbackQuickActions,
        additionalData: {}
      };
    }
  }

  /**
   * Fetch investor dashboard data
   */
  private async fetchInvestorData(user: User | null): Promise<DashboardData> {
    return this.fetchUserData(user); // Investors use user data with investor-specific role
  }

  /**
   * Get fallback user data when API calls fail
   */
  private getFallbackUserData(): DashboardData {
    const stats: DashboardStat[] = [
      {
        id: 'total_ideas',
        title: 'Total Ideas',
        value: 0,
        icon: Lightbulb,
        color: 'bg-indigo-600/30',
        change: 0,
        changeType: 'neutral',
        description: 'Your submitted business ideas'
      }
    ];

    const quickActions: DashboardQuickAction[] = [
      {
        id: 'create_idea',
        title: 'Create Business Idea',
        description: 'Submit a new business idea',
        icon: Lightbulb,
        href: '/dashboard/business-ideas/create',
        color: 'bg-indigo-600/20 hover:bg-indigo-600/30 border-indigo-500/30',
      }
    ];

    return { stats, quickActions };
  }

  /**
   * Clear cache for a specific user/role
   */
  clearCache(user: User | null, role?: DashboardRole): void {
    const targetRole = role || (getUserRole(user) as DashboardRole);
    const cacheKey = `${targetRole}_${user?.id || 'anonymous'}`;
    this.cache.delete(cacheKey);
  }

  /**
   * Clear all cache
   */
  clearAllCache(): void {
    this.cache.clear();
  }

  /**
   * Fallback admin data when API fails
   */
  private getFallbackAdminData(): DashboardData {
    const stats: DashboardStat[] = [
      {
        id: 'total_users',
        title: 'Total Users',
        value: 0,
        icon: Users,
        color: 'bg-blue-600/30',
        change: 0,
        changeType: 'increase',
        description: 'Registered platform users'
      }
    ];

    const quickActions: DashboardQuickAction[] = [
      {
        id: 'user_management',
        title: 'User Management',
        description: 'Manage users, roles, and permissions',
        icon: Users,
        href: '/admin/users',
        color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
      }
    ];

    return {
      stats,
      quickActions,
      additionalData: {
        systemHealth: { status: 'unknown', uptime: 'N/A' },
        recentActivity: []
      }
    };
  }
}

// Export singleton instance
export const dashboardDataService = DashboardDataService.getInstance();

/**
 * ROLE PROTECTED ROUTE TESTS
 * Tests for unified route protection component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { RoleProtectedRoute, useRoleAccess } from '../RoleProtectedRoute';
import { UserRole } from '../../../utils/unifiedRoleManager';

// Mock the unified role manager
jest.mock('../../../utils/unifiedRoleManager', () => ({
  getUserRole: jest.fn(),
  UserRole: {}
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Navigate: ({ to }: { to: string }) => {
    mockNavigate(to);
    return <div data-testid="navigate-to">{to}</div>;
  },
  useLocation: () => ({ pathname: '/test-path' })
}));

// Mock translations
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue || key
  })
}));

// Create mock store
const createMockStore = (authState: any) => {
  return configureStore({
    reducer: {
      auth: (state = authState) => state
    }
  });
};

// Mock users for testing
const mockUsers = {
  superAdmin: {
    id: 1,
    username: 'superadmin',
    user_role: 'super_admin' as UserRole,
    is_superuser: true,
    is_staff: true
  },
  admin: {
    id: 2,
    username: 'admin',
    user_role: 'admin' as UserRole,
    is_superuser: false,
    is_staff: true
  },
  user: {
    id: 3,
    username: 'user',
    user_role: 'user' as UserRole,
    is_superuser: false,
    is_staff: false
  }
};

const TestWrapper: React.FC<{ children: React.ReactNode; authState: any }> = ({ 
  children, 
  authState 
}) => {
  const store = createMockStore(authState);
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </Provider>
  );
};

describe('RoleProtectedRoute Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication Requirements', () => {
    test('should show loading state when authentication is loading', () => {
      const authState = {
        user: null,
        isAuthenticated: false,
        isLoading: true
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute allowedRoles={['user']}>
            <div>Protected Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByRole('status')).toBeInTheDocument();
    });

    test('should redirect to login when user is not authenticated', () => {
      const authState = {
        user: null,
        isAuthenticated: false,
        isLoading: false
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute allowedRoles={['user']} requireAuth={true}>
            <div>Protected Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByTestId('navigate-to')).toHaveTextContent('/login');
    });

    test('should allow access when requireAuth is false and user is not authenticated', () => {
      const authState = {
        user: null,
        isAuthenticated: false,
        isLoading: false
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute allowedRoles={['user']} requireAuth={false}>
            <div>Public Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Public Content')).toBeInTheDocument();
    });
  });

  describe('Role-based Access Control', () => {
    test('should allow access when user has required role', () => {
      const { getUserRole } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('admin');

      const authState = {
        user: mockUsers.admin,
        isAuthenticated: true,
        isLoading: false
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute allowedRoles={['admin', 'super_admin']}>
            <div>Admin Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Admin Content')).toBeInTheDocument();
    });

    test('should deny access when user does not have required role', () => {
      const { getUserRole, getDashboardRoute } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('user');
      getDashboardRoute.mockReturnValue('/dashboard');

      const authState = {
        user: mockUsers.user,
        isAuthenticated: true,
        isLoading: false
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute allowedRoles={['admin', 'super_admin']}>
            <div>Admin Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByTestId('navigate-to')).toHaveTextContent('/dashboard');
    });

    test('should show unauthorized message when showUnauthorized is true', () => {
      const { getUserRole } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('user');

      const authState = {
        user: mockUsers.user,
        isAuthenticated: true,
        isLoading: false
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute allowedRoles={['admin']} showUnauthorized={true}>
            <div>Admin Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText('You do not have permission to access this page.')).toBeInTheDocument();
    });

    test('should use custom fallback path when provided', () => {
      const { getUserRole } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('user');

      const authState = {
        user: mockUsers.user,
        isAuthenticated: true,
        isLoading: false
      };

      render(
        <TestWrapper authState={authState}>
          <RoleProtectedRoute 
            allowedRoles={['admin']} 
            fallbackPath="/custom-fallback"
          >
            <div>Admin Content</div>
          </RoleProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByTestId('navigate-to')).toHaveTextContent('/custom-fallback');
    });
  });

  describe('Convenience Components', () => {
    test('SuperAdminRoute should only allow super_admin', () => {
      const { getUserRole } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('admin');

      const authState = {
        user: mockUsers.admin,
        isAuthenticated: true,
        isLoading: false
      };

      const { SuperAdminRoute } = require('../RoleProtectedRoute');

      render(
        <TestWrapper authState={authState}>
          <SuperAdminRoute>
            <div>Super Admin Content</div>
          </SuperAdminRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });

    test('AdminRoute should allow admin and super_admin', () => {
      const { getUserRole } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('admin');

      const authState = {
        user: mockUsers.admin,
        isAuthenticated: true,
        isLoading: false
      };

      const { AdminRoute } = require('../RoleProtectedRoute');

      render(
        <TestWrapper authState={authState}>
          <AdminRoute>
            <div>Admin Content</div>
          </AdminRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Admin Content')).toBeInTheDocument();
    });

    test('BusinessUserRoute should allow user, mentor, investor', () => {
      const { getUserRole } = require('../../../utils/unifiedRoleManager');
      getUserRole.mockReturnValue('user');

      const authState = {
        user: mockUsers.user,
        isAuthenticated: true,
        isLoading: false
      };

      const { BusinessUserRoute } = require('../RoleProtectedRoute');

      render(
        <TestWrapper authState={authState}>
          <BusinessUserRoute>
            <div>Business Content</div>
          </BusinessUserRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Business Content')).toBeInTheDocument();
    });
  });
});

describe('useRoleAccess Hook Tests', () => {
  test('should return correct role information', () => {
    const { getUserRole } = require('../../../utils/unifiedRoleManager');
    getUserRole.mockReturnValue('admin');

    const authState = {
      user: mockUsers.admin,
      isAuthenticated: true,
      isLoading: false
    };

    const TestComponent = () => {
      const roleAccess = useRoleAccess();
      
      return (
        <div>
          <div data-testid="user-role">{roleAccess.userRole}</div>
          <div data-testid="is-admin">{roleAccess.isAdmin.toString()}</div>
          <div data-testid="is-super-admin">{roleAccess.isSuperAdmin.toString()}</div>
          <div data-testid="is-business-user">{roleAccess.isBusinessUser.toString()}</div>
        </div>
      );
    };

    render(
      <TestWrapper authState={authState}>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('user-role')).toHaveTextContent('admin');
    expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
    expect(screen.getByTestId('is-super-admin')).toHaveTextContent('false');
    expect(screen.getByTestId('is-business-user')).toHaveTextContent('false');
  });

  test('should correctly identify super admin', () => {
    const { getUserRole } = require('../../../utils/unifiedRoleManager');
    getUserRole.mockReturnValue('super_admin');

    const authState = {
      user: mockUsers.superAdmin,
      isAuthenticated: true,
      isLoading: false
    };

    const TestComponent = () => {
      const roleAccess = useRoleAccess();
      
      return (
        <div>
          <div data-testid="is-super-admin">{roleAccess.isSuperAdmin.toString()}</div>
          <div data-testid="is-admin">{roleAccess.isAdmin.toString()}</div>
        </div>
      );
    };

    render(
      <TestWrapper authState={authState}>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('is-super-admin')).toHaveTextContent('true');
    expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
  });
});

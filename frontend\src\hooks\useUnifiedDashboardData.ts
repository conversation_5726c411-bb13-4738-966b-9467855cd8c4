/**
 * Unified Dashboard Data Hook
 * Consolidates all dashboard data fetching logic into a single hook
 * Replaces multiple scattered hooks like useDashboardData, useBusinessIdeas, etc.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAppSelector } from '../store/hooks';
import { DashboardRole, DashboardStat, DashboardQuickAction } from '../types/dashboard';
import { dashboardDataService, DashboardData } from '../services/dashboardDataService';
import { getRefreshInterval } from '../utils/dashboardUtils';
import { getUserRole } from '../utils/unifiedRoleManager';

interface UseUnifiedDashboardDataOptions {
  role?: DashboardRole;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onError?: (error: Error) => void;
  onDataUpdate?: (data: DashboardData) => void;
}

interface UseUnifiedDashboardDataResult {
  // Core data
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  additionalData: Record<string, any>;
  
  // State
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  
  // Actions
  refreshData: () => Promise<void>;
  clearCache: () => void;
  
  // Role info
  role: DashboardRole;
  
  // Legacy compatibility (for existing components)
  businessIdeas?: any[];
  latestIdea?: any;
  systemHealth?: any;
  systemMetrics?: any;
  securityAlerts?: any[];
  pendingReports?: any[];
  recentActivity?: any[];
  recentIdeas?: any[];
}

/**
 * Unified dashboard data hook that works for all user roles
 */
export const useUnifiedDashboardData = (
  options: UseUnifiedDashboardDataOptions = {}
): UseUnifiedDashboardDataResult => {
  const { user } = useAppSelector(state => state.auth);
  const {
    role: forcedRole,
    autoRefresh = true,
    refreshInterval: customRefreshInterval,
    onError,
    onDataUpdate
  } = options;

  // Determine role
  const role = forcedRole || (getUserRole(user) as DashboardRole);
  
  // State
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [quickActions, setQuickActions] = useState<DashboardQuickAction[]>([]);
  const [additionalData, setAdditionalData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Refs for cleanup
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // Fetch dashboard data
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!mountedRef.current) return;

    try {
      setLoading(true);
      setError(null);

      const data = await dashboardDataService.getDashboardData(user, forceRefresh);
      
      if (!mountedRef.current) return;

      setStats(data.stats);
      setQuickActions(data.quickActions);
      setAdditionalData(data.additionalData || {});
      setLastUpdated(new Date());

      // Call update callback
      onDataUpdate?.(data);
    } catch (err) {
      if (!mountedRef.current) return;

      const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard data';
      setError(errorMessage);
      
      // Call error callback
      onError?.(err instanceof Error ? err : new Error(errorMessage));
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [user, onError, onDataUpdate]);

  // Refresh data function
  const refreshData = useCallback(async () => {
    await fetchData(true);
  }, [fetchData]);

  // Clear cache function
  const clearCache = useCallback(() => {
    dashboardDataService.clearCache(user, role);
  }, [user, role]);

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = customRefreshInterval || getRefreshInterval(role);
    
    refreshIntervalRef.current = setInterval(() => {
      fetchData(false); // Use cache if available
    }, interval);

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [autoRefresh, customRefreshInterval, role, fetchData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  // Legacy compatibility - extract specific data types from additionalData
  const businessIdeas = additionalData.recentIdeas || additionalData.businessIdeas || [];
  const latestIdea = additionalData.latestIdea || null;
  const systemHealth = additionalData.systemHealth || null;
  const systemMetrics = additionalData.systemMetrics || null;
  const securityAlerts = additionalData.securityAlerts || [];
  const pendingReports = additionalData.pendingReports || [];
  const recentActivity = additionalData.recentActivity || [];
  const recentIdeas = additionalData.recentIdeas || [];

  return {
    // Core data
    stats,
    quickActions,
    additionalData,
    
    // State
    loading,
    error,
    lastUpdated,
    
    // Actions
    refreshData,
    clearCache,
    
    // Role info
    role,
    
    // Legacy compatibility
    businessIdeas,
    latestIdea,
    systemHealth,
    systemMetrics,
    securityAlerts,
    pendingReports,
    recentActivity,
    recentIdeas,
  };
};

/**
 * Legacy hook for backward compatibility with existing useBusinessIdeas hook
 */
export const useBusinessIdeas = () => {
  const { businessIdeas, latestIdea, loading, error, refreshData } = useUnifiedDashboardData();
  
  // Calculate stats from business ideas
  const stats = {
    totalIdeas: businessIdeas.length,
    approvedIdeas: businessIdeas.filter((idea: any) => idea.status === 'approved' || idea.moderation_status === 'approved').length,
    pendingIdeas: businessIdeas.filter((idea: any) => idea.status === 'pending' || idea.moderation_status === 'pending').length,
    rejectedIdeas: businessIdeas.filter((idea: any) => idea.status === 'rejected' || idea.moderation_status === 'rejected').length,
  };

  return {
    businessIdeas,
    loading,
    error,
    stats,
    latestIdea,
    refreshData
  };
};

/**
 * Legacy hook for backward compatibility with existing admin useDashboardData hook
 */
export const useDashboardData = () => {
  const { stats, loading, error, refreshData, recentActivity } = useUnifiedDashboardData();
  
  return {
    stats,
    recentActivity,
    isLoading: loading,
    error,
    refreshData
  };
};

export default useUnifiedDashboardData;

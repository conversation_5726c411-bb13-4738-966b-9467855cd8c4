import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  DollarSign,
  TrendingUp,
  PieChart,
  Search,
  BarChart3,
  Users,
  Calendar,
  Star,
  ArrowRight,
  Plus,
  Target,
  AlertCircle,
  CheckCircle,
  Clock,
  Award,
  Briefcase
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';
import { apiClient } from '../../services/api';

const InvestmentsDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not an investor
  useEffect(() => {
    if (isAuthenticated && userRole !== 'investor') {
      console.warn('Access denied: User is not an investor');
      navigate('/dashboard');
    }
  }, [isAuthenticated, userRole, navigate]);

  // ✅ REAL DATA: Initialize with empty state and load from API
  const [investmentStats, setInvestmentStats] = useState(null);
  const [recentOpportunities, setRecentOpportunities] = useState([]);

  // ✅ REAL DATA: Initialize with empty state and load from API
  const [portfolioCompanies, setPortfolioCompanies] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch portfolio companies from API
  useEffect(() => {
    const fetchPortfolioData = async () => {
      try {
        setLoading(true);

        // ✅ REAL DATA: Fetch investment data from API
        const [statsResponse, portfolioResponse, opportunitiesResponse] = await Promise.all([
          apiClient.get('/api/roles/investor/dashboard-stats/'),
          apiClient.get('/api/roles/investor/portfolio-companies/'),
          apiClient.get('/api/roles/investor/opportunities/')
        ]);

        setInvestmentStats(statsResponse.data);
        setPortfolioCompanies(portfolioResponse.data.results || []);
        setRecentOpportunities(opportunitiesResponse.data.results || []);

      } catch (error) {
        console.error('Failed to fetch portfolio companies:', error);

        // ✅ REAL DATA: Show error state instead of mock data
        setInvestmentStats(null);
        setPortfolioCompanies([]);
        setRecentOpportunities([]);

        // TODO: Show user-friendly error message
        // toast.error(t('errors.failedToLoadPortfolio'));

      } finally {
        setLoading(false);
      }
    };

    fetchPortfolioData();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value}%`;
  };

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'low': return 'bg-green-600/20 text-green-400';
      case 'medium': return 'bg-yellow-600/20 text-yellow-400';
      case 'high': return 'bg-red-600/20 text-red-400';
      default: return 'bg-gray-600/20 text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'performing': return 'bg-green-600/20 text-green-400';
      case 'underperforming': return 'bg-red-600/20 text-red-400';
      case 'stable': return 'bg-blue-600/20 text-blue-400';
      default: return 'bg-gray-600/20 text-gray-400';
    }
  };

  // Don't render if not authenticated or not an investor
  if (!isAuthenticated || userRole !== 'investor') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-300">This page is only accessible to investors.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white">Investment Dashboard</h1>
            <p className="text-gray-300 mt-1">
              Track your investments and discover new opportunities
            </p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <Link
              to="/dashboard/investment/opportunities"
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center"
            >
              <Search size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Browse Opportunities
            </Link>
            <Link
              to="/dashboard/investments/due-diligence"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center"
            >
              <BarChart3 size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Due Diligence
            </Link>
          </div>
        </div>

        {/* Investment Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Investments</p>
                <p className="text-2xl font-bold text-white">{investmentStats.totalInvestments}</p>
              </div>
              <Briefcase className="text-blue-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Invested</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(investmentStats.totalInvested)}</p>
              </div>
              <DollarSign className="text-green-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Portfolio Value</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(investmentStats.portfolioValue)}</p>
              </div>
              <PieChart className="text-purple-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Deals</p>
                <p className="text-2xl font-bold text-white">{investmentStats.activeDeals}</p>
              </div>
              <Target className="text-yellow-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Average ROI</p>
                <p className="text-2xl font-bold text-white">{formatPercentage(investmentStats.roi)}</p>
              </div>
              <TrendingUp className="text-orange-400" size={24} />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Opportunities */}
          <div>
            <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Search size={20} className={`mr-2 text-green-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
                Recent Opportunities
              </h2>
              <Link
                to="/dashboard/investment/opportunities"
                className="text-green-400 hover:text-green-300 flex items-center text-sm"
              >
                View All <ArrowRight size={16} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
              </Link>
            </div>

            <div className="space-y-4">
              {recentOpportunities.map(opportunity => (
                <div key={opportunity.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                  <div className={`flex justify-between items-start mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div>
                      <h3 className="font-semibold text-white">{opportunity.companyName}</h3>
                      <p className="text-gray-400 text-sm">{opportunity.industry}</p>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${getRiskColor(opportunity.riskLevel)}`}>
                      {opportunity.riskLevel} Risk
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <p className="text-gray-400">Round</p>
                      <p className="text-white">{opportunity.fundingRound}</p>
                    </div>
                    <div>
                      <p className="text-gray-400">Expected ROI</p>
                      <p className="text-white">{formatPercentage(opportunity.expectedROI)}</p>
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-400">Funding Progress</span>
                      <span className="text-white">
                        {formatCurrency(opportunity.raised)} / {formatCurrency(opportunity.targetAmount)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${(opportunity.raised / opportunity.targetAmount) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className="text-xs text-gray-400">
                      Deadline: {new Date(opportunity.deadline).toLocaleDateString()}
                    </span>
                    <Link
                      to={`/dashboard/investment/opportunities/${opportunity.id}`}
                      className="text-green-400 hover:text-green-300 text-sm flex items-center"
                    >
                      View Details <ArrowRight size={14} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Portfolio Performance */}
          <div>
            <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h2 className="text-xl font-semibold text-white flex items-center">
                <PieChart size={20} className={`mr-2 text-purple-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
                Portfolio Performance
              </h2>
              <Link
                to="/dashboard/investment/portfolio"
                className="text-purple-400 hover:text-purple-300 flex items-center text-sm"
              >
                View Portfolio <ArrowRight size={16} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
              </Link>
            </div>

            <div className="space-y-4">
              {portfolioCompanies.map(company => (
                <div key={company.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                  <div className={`flex justify-between items-start mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div>
                      <h3 className="font-semibold text-white">{company.name}</h3>
                      <p className="text-gray-400 text-sm">{company.industry}</p>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${getStatusColor(company.status)}`}>
                      {company.status}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <p className="text-gray-400">Invested</p>
                      <p className="text-white">{formatCurrency(company.investmentAmount)}</p>
                    </div>
                    <div>
                      <p className="text-gray-400">Current Value</p>
                      <p className="text-white">{formatCurrency(company.currentValue)}</p>
                    </div>
                  </div>

                  <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className={`text-sm font-medium ${company.roi >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      ROI: {formatPercentage(company.roi)}
                    </span>
                    <span className="text-xs text-gray-400">
                      Updated: {new Date(company.lastUpdate).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
          <Link
            to="/dashboard/investments/analytics"
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center mb-3">
              <BarChart3 className="text-blue-400 mr-3" size={24} />
              <h3 className="text-lg font-medium text-white">Investment Analytics</h3>
            </div>
            <p className="text-gray-400 text-sm">Detailed performance analysis and insights</p>
          </Link>

          <Link
            to="/dashboard/investments/due-diligence"
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center mb-3">
              <Search className="text-green-400 mr-3" size={24} />
              <h3 className="text-lg font-medium text-white">Due Diligence</h3>
            </div>
            <p className="text-gray-400 text-sm">Research and evaluate investment opportunities</p>
          </Link>

          <Link
            to="/dashboard/investments/profile"
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center mb-3">
              <Award className="text-purple-400 mr-3" size={24} />
              <h3 className="text-lg font-medium text-white">Investor Profile</h3>
            </div>
            <p className="text-gray-400 text-sm">Manage your investor profile and preferences</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default InvestmentsDashboardPage;

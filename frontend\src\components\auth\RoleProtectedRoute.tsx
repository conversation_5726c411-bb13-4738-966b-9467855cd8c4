/**
 * ROLE PROTECTED ROUTE COMPONENT
 * Unified route protection using the authoritative RBAC system
 * Replaces all inconsistent route protection logic throughout the app
 */

import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { getUserRole, getDashboardRoute, UserRole } from '../../utils/unifiedRoleManager';
import { useTranslation } from 'react-i18next';

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  fallbackPath?: string;
  requireAuth?: boolean;
  showUnauthorized?: boolean;
}

/**
 * AUTHORITATIVE ROUTE PROTECTION
 * Single component for all route protection needs
 */
export const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  allowedRoles,
  fallbackPath,
  requireAuth = true,
  showUnauthorized = false
}) => {
  const { t } = useTranslation();
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // Show loading state while authentication is being determined
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If authentication is not required and user is not authenticated, allow access
  if (!requireAuth && !isAuthenticated) {
    return <>{children}</>;
  }

  // Get user's role using unified role manager
  const userRole = getUserRole(user);

  // Check if user's role is allowed
  const hasAccess = allowedRoles.includes(userRole);

  if (!hasAccess) {
    if (showUnauthorized) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-50">
          <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {t('auth.unauthorized.title', 'Access Denied')}
            </h2>
            <p className="text-gray-600 mb-4">
              {t('auth.unauthorized.message', 'You do not have permission to access this page.')}
            </p>
            <p className="text-sm text-gray-500 mb-6">
              {t('auth.unauthorized.roleInfo', 'Your current role: {{role}}', { role: userRole })}
            </p>
            <button
              onClick={() => window.history.back()}
              className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              {t('common.goBack', 'Go Back')}
            </button>
          </div>
        </div>
      );
    }

    // Redirect to appropriate dashboard or fallback path
    const redirectPath = fallbackPath || getDashboardRoute(user);
    return <Navigate to={redirectPath} replace />;
  }

  // User has access, render the protected content
  return <>{children}</>;
};

/**
 * CONVENIENCE COMPONENTS FOR COMMON ROLE COMBINATIONS
 */

// ✅ UNIFIED: Super Admin Only (using unified role manager)
export const SuperAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);
  const isSuperAdmin = userRole === 'super_admin';

  return isSuperAdmin ? <>{children}</> : <Navigate to="/access-denied" replace />;
};

// ✅ UNIFIED: Admin and Super Admin (using role hierarchy)
export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);
  const isAdminOrHigher = userRole === 'admin' || userRole === 'super_admin';

  return isAdminOrHigher ? <>{children}</> : <Navigate to="/access-denied" replace />;
};

// ✅ UNIFIED: Business Users (User, Mentor, Investor)
export const BusinessUserRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);
  const isBusinessUser = userRole === 'user' || userRole === 'mentor' || userRole === 'investor';

  return isBusinessUser ? <>{children}</> : <Navigate to="/access-denied" replace />;
};

// ✅ UNIFIED: Mentor Only (using unified role manager)
export const MentorRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);
  const isMentor = userRole === 'mentor';

  return isMentor ? <>{children}</> : <Navigate to="/access-denied" replace />;
};

// ✅ UNIFIED: Investor Only (using unified role manager)
export const InvestorRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);
  const isInvestor = userRole === 'investor';

  return isInvestor ? <>{children}</> : <Navigate to="/access-denied" replace />;
};

// ✅ UNIFIED: Moderator Only (using unified role manager)
export const ModeratorRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);
  const isModerator = userRole === 'moderator';

  return isModerator ? <>{children}</> : <Navigate to="/access-denied" replace />;
};

// All Authenticated Users
export const AuthenticatedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleProtectedRoute allowedRoles={['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator']}>
    {children}
  </RoleProtectedRoute>
);

/**
 * ✅ UNIFIED: ROLE-BASED CONDITIONAL RENDERING HOOK
 * Use this for conditional rendering within components
 * Uses unified role manager functions - NO MORE HARDCODED ARRAYS
 */
export const useRoleAccess = () => {
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);

  return {
    userRole,
    isSuperAdmin: userRole === 'super_admin',
    isAdmin: userRole === 'admin' || userRole === 'super_admin', // ✅ UNIFIED: No hardcoded array
    isModerator: userRole === 'moderator',
    isMentor: userRole === 'mentor',
    isInvestor: userRole === 'investor',
    isBusinessUser: userRole === 'user' || userRole === 'mentor' || userRole === 'investor', // ✅ UNIFIED: No hardcoded array
    hasRole: (roles: UserRole[]) => roles.includes(userRole)
    // ✅ REMOVED: canAccess - use hasAnyRole() from unifiedRoleManager instead
  };
};

export default RoleProtectedRoute;

/**
 * Consolidated Welcome Section
 * Replaces duplicate WelcomeSection components from user and admin dashboards
 * Adapts to different user roles while sharing common functionality
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { RTLText } from '../../common';
import { User } from '../../../store/authSlice';
import { getUserRole } from '../../../utils/unifiedRoleManager';
import { 
  ShieldAlert, 
  TrendingUp, 
  Users, 
  Activity, 
  Shield,
  Crown,
  UserCheck,
  Briefcase,
  Eye,
  Lightbulb
} from 'lucide-react';

interface ConsolidatedWelcomeSectionProps {
  user: User | null;
  className?: string;
  showStats?: boolean;
  showSecurityNotice?: boolean;
  customTitle?: string;
  customSubtitle?: string;
}

interface WelcomeStats {
  activeUsers: number;
  todayActivity: number;
  growthRate: number;
  systemStatus: 'online' | 'offline' | 'maintenance';
}

const ConsolidatedWelcomeSection: React.FC<ConsolidatedWelcomeSectionProps> = ({
  user,
  className = '',
  showStats,
  showSecurityNotice,
  customTitle,
  customSubtitle,
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const role = getUserRole(user);

  const [stats, setStats] = useState<WelcomeStats>({
    activeUsers: 0,
    todayActivity: 0,
    growthRate: 0,
    systemStatus: 'online'
  });
  const [loading, setLoading] = useState(true);

  // Determine if stats should be shown based on role
  const shouldShowStats = showStats ?? (role === 'admin' || role === 'super_admin' || role === 'moderator');
  
  // Determine if security notice should be shown based on role
  const shouldShowSecurityNotice = showSecurityNotice ?? (role === 'admin' || role === 'super_admin');

  // Fetch role-specific welcome data
  useEffect(() => {
    if (!shouldShowStats) {
      setLoading(false);
      return;
    }

    const fetchWelcomeData = async () => {
      try {
        setLoading(true);
        
        // Simulate API call - in real implementation, this would be role-specific
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data based on role
        const mockStats = getRoleSpecificStats(role);
        setStats(mockStats);
      } catch (error) {
        console.error('Error fetching welcome data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWelcomeData();
  }, [role, shouldShowStats]);

  // Get role-specific mock stats
  const getRoleSpecificStats = (userRole: string): WelcomeStats => {
    const baseStats = {
      systemStatus: 'online' as const,
    };

    switch (userRole) {
      case 'super_admin':
        return {
          ...baseStats,
          activeUsers: 1247,
          todayActivity: 3456,
          growthRate: 12.5,
        };
      case 'admin':
        return {
          ...baseStats,
          activeUsers: 856,
          todayActivity: 2134,
          growthRate: 8.3,
        };
      case 'moderator':
        return {
          ...baseStats,
          activeUsers: 234,
          todayActivity: 567,
          growthRate: 5.2,
        };
      default:
        return {
          ...baseStats,
          activeUsers: 12,
          todayActivity: 34,
          growthRate: 7.8,
        };
    }
  };

  // Get role-specific content
  const getRoleContent = () => {
    const username = user?.username || t('common.user', 'User');
    
    // Use custom title/subtitle if provided
    if (customTitle || customSubtitle) {
      return {
        title: customTitle || t('dashboard.welcome.default', 'Welcome, {{username}}!', { username }),
        subtitle: customSubtitle || t('dashboard.subtitle.default', 'Your dashboard'),
        icon: Lightbulb,
        iconColor: 'text-indigo-400',
      };
    }
    
    switch (role) {
      case 'super_admin':
        return {
          title: t('dashboard.welcome.superAdmin', 'Welcome back, Super Admin {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.superAdmin', 'Complete system control and monitoring'),
          icon: Crown,
          iconColor: 'text-red-400',
        };
      case 'admin':
        return {
          title: t('dashboard.welcome.admin', 'Welcome back, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.admin', 'Platform administration and management'),
          icon: Shield,
          iconColor: 'text-purple-400',
        };
      case 'moderator':
        return {
          title: t('dashboard.welcome.moderator', 'Welcome, Moderator {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.moderator', 'Monitor and manage community content'),
          icon: Eye,
          iconColor: 'text-blue-400',
        };
      case 'mentor':
        return {
          title: t('dashboard.welcome.mentor', 'Welcome, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.mentor', 'Guide and support your mentees'),
          icon: UserCheck,
          iconColor: 'text-blue-400',
        };
      case 'investor':
        return {
          title: t('dashboard.welcome.investor', 'Welcome, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.investor', 'Track investments and opportunities'),
          icon: Briefcase,
          iconColor: 'text-green-400',
        };
      default: // user
        return {
          title: t('dashboard.welcome.user', 'Welcome, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.user', 'Your personal dashboard'),
          icon: Lightbulb,
          iconColor: 'text-indigo-400',
        };
    }
  };

  const roleContent = getRoleContent();
  const IconComponent = roleContent.icon;

  // Get role-specific accent colors while using glass morphism base
  const getRoleAccent = () => {
    switch (role) {
      case 'super_admin':
        return 'border-red-500/30';
      case 'admin':
        return 'border-purple-500/30';
      case 'moderator':
        return 'border-blue-500/30';
      case 'mentor':
        return 'border-blue-400/30';
      case 'investor':
        return 'border-green-500/30';
      default:
        return 'border-glass-border';
    }
  };

  const roleAccent = getRoleAccent();

  const getSystemStatusColor = () => {
    switch (stats.systemStatus) {
      case 'online':
        return 'text-green-400 bg-green-400';
      case 'maintenance':
        return 'text-yellow-400 bg-yellow-400';
      case 'offline':
        return 'text-red-400 bg-red-400';
      default:
        return 'text-green-400 bg-green-400';
    }
  };

  const getSystemStatusText = () => {
    switch (stats.systemStatus) {
      case 'online':
        return t('dashboard.system.online', 'System Online');
      case 'maintenance':
        return t('dashboard.system.maintenance', 'Under Maintenance');
      case 'offline':
        return t('dashboard.system.offline', 'System Offline');
      default:
        return t('dashboard.system.online', 'System Online');
    }
  };

  return (
    <div className={`glass-morphism rounded-2xl p-6 border ${roleAccent} ${className}`}>
      {/* Welcome Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`p-3 glass-light rounded-lg ${isRTL ? 'ml-4' : 'mr-4'}`}>
            <IconComponent className={`w-8 h-8 ${roleContent.iconColor}`} />
          </div>
          <div>
            <RTLText as="h1" className={`text-3xl font-bold text-glass-primary mb-2`}>
              {roleContent.title}
            </RTLText>
            <RTLText className={`text-glass-secondary text-lg`}>
              {roleContent.subtitle}
            </RTLText>
          </div>
        </div>
        
        {/* System Status (for admin roles) */}
        {shouldShowStats && (
          <div className="hidden md:flex items-center space-x-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`w-3 h-3 rounded-full animate-pulse ${isRTL ? 'ml-2' : 'mr-2'} ${getSystemStatusColor().split(' ')[1]}`}></div>
              <span className={`text-sm font-medium ${getSystemStatusColor().split(' ')[0]}`}>
                {getSystemStatusText()}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Quick Stats Overview (for admin/moderator roles) */}
      {shouldShowStats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="glass-light rounded-xl p-4 border border-glass-border">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Users size={20} className="text-blue-400" />
              </div>
              <div className={isRTL ? 'mr-3' : 'ml-3'}>
                <RTLText as="p" className={`text-sm text-glass-secondary`}>
                  {t('dashboard.stats.activeUsers', 'Active Users')}
                </RTLText>
                <RTLText as="p" className={`text-xl font-bold text-glass-primary`}>
                  {loading ? (
                    <span className="animate-pulse">---</span>
                  ) : (
                    stats.activeUsers.toLocaleString()
                  )}
                </RTLText>
              </div>
            </div>
          </div>

          <div className="glass-light rounded-xl p-4 border border-glass-border">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Activity size={20} className="text-purple-400" />
              </div>
              <div className={isRTL ? 'mr-3' : 'ml-3'}>
                <RTLText as="p" className={`text-sm text-glass-secondary`}>
                  {t('dashboard.stats.todayActivity', 'Today\'s Activity')}
                </RTLText>
                <RTLText as="p" className={`text-xl font-bold text-glass-primary`}>
                  {loading ? (
                    <span className="animate-pulse">---</span>
                  ) : (
                    stats.todayActivity.toLocaleString()
                  )}
                </RTLText>
              </div>
            </div>
          </div>

          <div className="glass-light rounded-xl p-4 border border-glass-border">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-green-500/20 rounded-lg">
                <TrendingUp size={20} className="text-green-400" />
              </div>
              <div className={isRTL ? 'mr-3' : 'ml-3'}>
                <RTLText as="p" className={`text-sm text-glass-secondary`}>
                  {t('dashboard.stats.growthRate', 'Growth Rate')}
                </RTLText>
                <RTLText as="p" className={`text-xl font-bold text-glass-primary`}>
                  {loading ? (
                    <span className="animate-pulse">---</span>
                  ) : (
                    `${stats.growthRate > 0 ? '+' : ''}${stats.growthRate.toFixed(1)}%`
                  )}
                </RTLText>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Notice (for admin roles) */}
      {shouldShowSecurityNotice && (
        <div className="mt-6 p-4 bg-gradient-to-r from-red-900/30 to-orange-900/30 border border-red-500/30 rounded-xl">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-red-500/20 rounded-lg">
              <ShieldAlert size={20} className="text-red-500" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText as="h4" className="font-semibold text-red-300 mb-1">
                {t('dashboard.security.notice', 'Security Notice')}
              </RTLText>
              <RTLText as="p" className="text-sm text-red-200">
                {role === 'super_admin' 
                  ? t('dashboard.security.superAdminNote', 'You have complete system access. Handle with extreme care.')
                  : t('dashboard.security.adminNote', 'You are accessing the admin panel. Handle data with care.')
                }
              </RTLText>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConsolidatedWelcomeSection;

/**
 * CENTRALIZED ROLE-ROUTE MAPPING
 * Single source of truth for all route-role relationships
 * Eliminates hardcoded role arrays throughout the application
 */

import { UserRole, PermissionLevel } from '../utils/unifiedRoleManager';

export interface RouteRoleMapping {
  path: string;
  allowedRoles: UserRole[];
  requiredPermissions: PermissionLevel[];
  requireAuth: boolean;
  category: 'public' | 'auth' | 'main' | 'content' | 'system' | 'security' | 'super_admin' | 'ai';
  description: string;
}

/**
 * AUTHORITATIVE ROUTE-ROLE MAPPING
 * This is the single source of truth for all route access control
 * Navigation configuration MUST match these definitions exactly
 */
export const ROUTE_ROLE_MAPPINGS: RouteRoleMapping[] = [
  // ===== PUBLIC ROUTES =====
  {
    path: '/',
    allowedRoles: [],
    requiredPermissions: [],
    requireAuth: false,
    category: 'public',
    description: 'Home page - accessible to all'
  },
  {
    path: '/login',
    allowedRoles: [],
    requiredPermissions: [],
    requireAuth: false,
    category: 'auth',
    description: 'Login page'
  },
  {
    path: '/register',
    allowedRoles: [],
    requiredPermissions: [],
    requireAuth: false,
    category: 'auth',
    description: 'Registration page'
  },

  // ===== MAIN DASHBOARD ROUTES =====
  {
    path: '/dashboard',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Main dashboard - all authenticated users'
  },

  // ===== BUSINESS ROUTES (User + Advisors) =====
  {
    path: '/dashboard/business-ideas',
    allowedRoles: ['user', 'mentor', 'investor'], // FIXED: Allow advisors to view/mentor
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Business ideas - creators and advisors'
  },
  {
    path: '/dashboard/business-ideas/new',
    allowedRoles: ['user', 'mentor', 'investor'],
    requiredPermissions: ['write'],
    requireAuth: true,
    category: 'main',
    description: 'Create new business idea'
  },
  {
    path: '/dashboard/business-plans',
    allowedRoles: ['user', 'mentor', 'investor'], // FIXED: Allow advisors to view/mentor
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Business plans - creators and advisors'
  },
  {
    path: '/dashboard/incubator',
    allowedRoles: ['user', 'mentor', 'investor'], // FIXED: Allow advisors access
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Incubator programs - creators and advisors'
  },
  {
    path: '/dashboard/templates',
    allowedRoles: ['user', 'mentor', 'investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Business plan templates'
  },

  // ===== CONTENT ROUTES =====
  {
    path: '/dashboard/posts',
    allowedRoles: ['user', 'mentor', 'investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Community posts'
  },
  {
    path: '/dashboard/events',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Events and workshops'
  },
  {
    path: '/dashboard/resources',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Learning resources'
  },
  {
    path: '/dashboard/forums',
    allowedRoles: ['user', 'mentor', 'investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Community forums'
  },
  {
    path: '/dashboard/chat',
    allowedRoles: ['user', 'mentor', 'investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Direct messaging'
  },

  // ===== ANALYTICS ROUTES =====
  {
    path: '/dashboard/analytics',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Analytics dashboard'
  },

  // ===== FUNDING ROUTES =====
  {
    path: '/dashboard/funding',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Funding opportunities and resources'
  },

  // ===== MENTORSHIP ROUTES =====
  {
    path: '/dashboard/mentor',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Dedicated mentor dashboard - mentors only'
  },

  {
    path: '/dashboard/mentorship',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Main mentorship dashboard - mentors only'
  },
  {
    path: '/dashboard/mentorship/sessions',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Mentorship sessions - mentors only'
  },
  {
    path: '/dashboard/mentorship/mentees',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Mentee management - mentors only'
  },
  {
    path: '/dashboard/mentorship/calendar',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Mentorship calendar - mentors only'
  },
  {
    path: '/dashboard/mentorship/analytics',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Mentorship analytics - mentors only'
  },
  {
    path: '/dashboard/mentorship/availability',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read', 'write'],
    requireAuth: true,
    category: 'content',
    description: 'Mentor availability management - mentors only'
  },
  {
    path: '/dashboard/mentorship/profile',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read', 'write'],
    requireAuth: true,
    category: 'content',
    description: 'Mentor profile management - mentors only'
  },
  {
    path: '/dashboard/mentorship/resources',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Mentorship resources - mentors only'
  },
  {
    path: '/dashboard/mentorship/sessions/schedule',
    allowedRoles: ['mentor'],
    requiredPermissions: ['read', 'write'],
    requireAuth: true,
    category: 'content',
    description: 'Session scheduler - mentors only'
  },


  // ===== MODERATOR ROUTES =====
  {
    path: '/dashboard/moderator',
    allowedRoles: ['moderator'],
    requiredPermissions: ['moderate'],
    requireAuth: true,
    category: 'main',
    description: 'Moderator dashboard - moderators only'
  },
  {
    path: '/dashboard/moderation/forum',
    allowedRoles: ['moderator'],
    requiredPermissions: ['moderate'],
    requireAuth: true,
    category: 'content',
    description: 'Forum moderation - moderators only'
  },
  {
    path: '/dashboard/moderation/reports',
    allowedRoles: ['moderator'],
    requiredPermissions: ['moderate'],
    requireAuth: true,
    category: 'content',
    description: 'Reports management - moderators only'
  },
  {
    path: '/dashboard/moderation/analytics',
    allowedRoles: ['moderator'],
    requiredPermissions: ['moderate'],
    requireAuth: true,
    category: 'content',
    description: 'Moderation analytics - moderators only'
  },

  // ===== INVESTMENT ROUTES =====
  {
    path: '/dashboard/investor',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Investor dashboard - investors only'
  },
  {
    path: '/dashboard/investments',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Investments overview - investors only'
  },
  {
    path: '/dashboard/investments/analytics',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Investment analytics - investors only'
  },
  {
    path: '/dashboard/investments/due-diligence',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Due diligence - investors only'
  },
  {
    path: '/dashboard/investments/profile',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Investor profile - investors only'
  },
  {
    path: '/dashboard/investment/opportunities',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Investment opportunities - investors only'
  },
  {
    path: '/dashboard/investment/portfolio',
    allowedRoles: ['investor'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Investment portfolio - investors only'
  },

  // ===== MODERATION ROUTES =====
  {
    path: '/dashboard/moderation/content',
    allowedRoles: ['moderator'],
    requiredPermissions: ['moderate'],
    requireAuth: true,
    category: 'content',
    description: 'Content moderation - moderators only'
  },
  {
    path: '/dashboard/moderation/users',
    allowedRoles: ['moderator'],
    requiredPermissions: ['moderate'],
    requireAuth: true,
    category: 'content',
    description: 'User moderation - moderators only'
  },

  // ===== ADMIN ROUTES =====
  {
    path: '/admin',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'admin',
    description: 'Admin dashboard - admins only'
  },
  {
    path: '/admin/users',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'User management - admins only'
  },
  {
    path: '/admin/analytics',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'Admin analytics - admins only'
  },
  {
    path: '/admin/settings',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'System settings - admins only'
  },
  {
    path: '/admin/events',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'content',
    description: 'Events management - admins only'
  },
  {
    path: '/admin/resources',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'content',
    description: 'Resources management - admins only'
  },
  {
    path: '/admin/posts',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'content',
    description: 'Posts management - admins only'
  },
  {
    path: '/admin/moderation',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'content',
    description: 'Moderation dashboard - admins only'
  },
  {
    path: '/admin/communication',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'Communication center - admins only'
  },
  {
    path: '/admin/api',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'API management - admins only'
  },
  {
    path: '/admin/performance',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'Performance monitoring - admins only'
  },
  {
    path: '/admin/ai-system',
    allowedRoles: ['admin', 'super_admin'],
    requiredPermissions: ['admin'],
    requireAuth: true,
    category: 'system',
    description: 'AI system management - admins only'
  },

  // ===== SUPER ADMIN ROUTES =====
  {
    path: '/super_admin',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'Super admin dashboard'
  },
  {
    path: '/super_admin/monitoring',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'System monitoring'
  },
  {
    path: '/super_admin/ai-system-management',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'AI system management'
  },
  {
    path: '/super_admin/system-management',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'System management - super admins only'
  },
  {
    path: '/super_admin/user-impersonation',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'User impersonation - super admins only'
  },
  {
    path: '/super_admin/ai-configuration',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'AI configuration - super admins only'
  },
  {
    path: '/super_admin/ai-analytics',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'AI analytics - super admins only'
  },
  {
    path: '/super_admin/ai-monitoring',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'AI monitoring - super admins only'
  },
  {
    path: '/super_admin/security',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'Security management - super admins only'
  },
  {
    path: '/super_admin/performance',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'Performance monitoring - super admins only'
  },
  {
    path: '/super_admin/api',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'API management - super admins only'
  },
  {
    path: '/super_admin/communication',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'Communication center - super admins only'
  },
  {
    path: '/super_admin/system-logs',
    allowedRoles: ['super_admin'],
    requiredPermissions: ['super_admin'],
    requireAuth: true,
    category: 'super_admin',
    description: 'System logs - super admins only'
  },

  // ===== AI ROUTES =====
  {
    path: '/dashboard/ai',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'ai',
    description: 'AI Dashboard - all authenticated users'
  },
  {
    path: '/dashboard/forums',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'content',
    description: 'Community Forums - all authenticated users'
  },
  {
    path: '/dashboard/find-mentor',
    allowedRoles: ['user'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'Find mentor dashboard - for users seeking mentorship'
  },

  // ===== PROFILE ROUTES =====
  {
    path: '/profile',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'User profile - all authenticated users'
  },
  {
    path: '/settings',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    requiredPermissions: ['read'],
    requireAuth: true,
    category: 'main',
    description: 'User settings - all authenticated users'
  }
];

/**
 * Get route configuration by path
 */
export function getRouteConfig(path: string): RouteRoleMapping | undefined {
  return ROUTE_ROLE_MAPPINGS.find(route => route.path === path);
}

/**
 * Get all routes accessible by a specific role
 */
export function getRoutesForRole(role: UserRole): RouteRoleMapping[] {
  return ROUTE_ROLE_MAPPINGS.filter(route => 
    route.allowedRoles.length === 0 || route.allowedRoles.includes(role)
  );
}

/**
 * Check if a role can access a specific route
 */
export function canRoleAccessRoute(role: UserRole, path: string): boolean {
  const route = getRouteConfig(path);
  if (!route) return false;
  
  // Public routes (no roles required) are accessible to all
  if (route.allowedRoles.length === 0) return true;
  
  return route.allowedRoles.includes(role);
}

/**
 * Get routes by category
 */
export function getRoutesByCategory(category: RouteRoleMapping['category']): RouteRoleMapping[] {
  return ROUTE_ROLE_MAPPINGS.filter(route => route.category === category);
}

/**
 * Validate that all navigation items have corresponding routes
 */
export function validateNavigationRouteConsistency(navigationPaths: string[]): {
  valid: boolean;
  missingRoutes: string[];
  extraRoutes: string[];
} {
  const routePaths = ROUTE_ROLE_MAPPINGS.map(route => route.path);
  
  const missingRoutes = navigationPaths.filter(path => !routePaths.includes(path));
  const extraRoutes = routePaths.filter(path => 
    !navigationPaths.includes(path) && 
    !path.startsWith('/login') && 
    !path.startsWith('/register') && 
    path !== '/'
  );
  
  return {
    valid: missingRoutes.length === 0 && extraRoutes.length === 0,
    missingRoutes,
    extraRoutes
  };
}

/**
 * Universal Sidebar
 * Single sidebar component that adapts to ALL user types:
 * - Regular Users
 * - Admin Users  
 * - Super Admin Users
 * - Mentors, Investors, Moderators
 * 
 * Eliminates ALL sidebar duplications across the application
 */

import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { logout } from '../../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole, UserRole, getDashboardRoute } from '../../utils/unifiedRoleManager';
import { getNavigationItemsForRole } from '../../config/navigationConfig';
import { TEST_IDS } from '../../utils/testIds';
import './UniversalSidebar.css';
import {
  Home, FileText, Lightbulb, Settings, User, X, LogOut, ChevronRight, ChevronLeft,
  Calendar, BookOpen, BarChart3, MessageSquare, Users, Shield, Briefcase,
  TrendingUp, Heart, Search, Bot, Zap, Globe, Award, Target, DollarSign,
  Database, Monitor, Lock, AlertTriangle, Activity, PieChart, UserCheck,
  Building, Rocket, Star, Gift, Mail, Phone, HelpCircle, Book, Video,
  Image, Music, Code, Palette, Camera, Headphones, Gamepad2, Coffee, Sparkles
} from 'lucide-react';

interface UniversalSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  onToggle?: () => void;
  variant?: 'desktop' | 'mobile';
  isCollapsed?: boolean;
}

// Import NavItem interface from centralized config
import type { NavItem } from '../../config/navigationConfig';

// Use UserRole from unified role manager
type UserType = UserRole;

// Icon mapping for string-based icon names
const iconMap: Record<string, React.ComponentType<any>> = {
  Home,
  FileText,
  Lightbulb,
  Settings,
  User,
  Users,
  Calendar,
  BookOpen,
  BarChart3,
  MessageSquare,
  Shield,
  Briefcase,
  TrendingUp,
  Heart,
  Search,
  Bot,
  Zap,
  Globe,
  Award,
  Target,
  DollarSign,
  Database,
  Monitor,
  Lock,
  AlertTriangle,
  Activity,
  PieChart,
  UserCheck,
  Building,
  Rocket,
  Star,
  Gift,
  Mail,
  Phone,
  HelpCircle,
  Book,
  Video,
  Image,
  Music,
  Code,
  Palette,
  Camera,
  Headphones,
  Gamepad2,
  Coffee,
  Sparkles
};

// Helper function to get icon component from string name with error handling
const getIconComponent = (iconName: string) => {
  try {
    const IconComponent = iconMap[iconName];
    return IconComponent ? <IconComponent className="w-5 h-5" /> : <Home className="w-5 h-5" />;
  } catch (error) {
    // Graceful fallback for any icon rendering errors
    if (import.meta.env?.DEV) {
      console.warn(`Error rendering icon "${iconName}":`, error);
    }
    return <Home className="w-5 h-5" />;
  }
};

const UniversalSidebar: React.FC<UniversalSidebarProps> = React.memo(({
  isOpen = true,
  onClose,
  onToggle,
  variant = 'desktop',
  isCollapsed = false
}) => {
  const { user, isAuthenticated, isLoading } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { isRTL } = useLanguage();

  // ✅ UNIFIED: Use the unified role manager for consistent role detection - MEMOIZED
  const userType = React.useMemo(() => getUserRole(user), [user?.id, user?.role, user?.user_role, user?.is_staff, user?.is_superuser, user?.profile]);

  // Type guard functions for better type safety - MEMOIZED
  const isSuperAdminType = React.useCallback((type: UserType): type is 'super_admin' => type === 'super_admin', []);
  const isAdminType = React.useCallback((type: UserType): type is 'admin' => type === 'admin', []);
  const isMentorType = React.useCallback((type: UserType): type is 'mentor' => type === 'mentor', []);
  const isInvestorType = React.useCallback((type: UserType): type is 'investor' => type === 'investor', []);

  // ✅ UNIFIED NAVIGATION - Use role-specific navigation for better role separation
  const navItems: NavItem[] = React.useMemo(() => {
    // Get navigation items specific to the user's role (mentor gets mentor-specific items)
    const roleBasedItems = getRoleSpecificNavigation(userType);

    // Apply translations to the items
    return roleBasedItems.map(item => ({
      ...item,
      name: t(item.name, item.name) // Apply translation
    }));
  }, [userType, t]);

  // ✅ OPTIMIZED NAVIGATION HANDLER - Debounced to prevent rapid clicking and timeouts
  const handleNavigation = React.useCallback((path: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();

      // Debounce navigation to prevent rapid clicking issues
      const target = event.currentTarget as HTMLElement;
      if (target.dataset.navigating === 'true') {
        return; // Already navigating
      }

      target.dataset.navigating = 'true';
      target.style.pointerEvents = 'none';

      // Reset after navigation
      setTimeout(() => {
        target.dataset.navigating = 'false';
        target.style.pointerEvents = 'auto';
      }, 500);
    }

    try {
      navigate(path);
      if (onClose) onClose();
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }, [navigate, onClose]);








  // ✅ UNIFIED: Use unified role manager for permission checking - MEMOIZED
  const hasPermissionForItem = React.useCallback((item: NavItem): boolean => {
    // If auth is still loading, don't show any restricted items
    if (isLoading) {
      return false;
    }

    // Must be authenticated
    if (!isAuthenticated || !user) {
      return false;
    }

    // Use unified role manager to check if user has the required role
    return item.allowedRoles.includes(userType);
  }, [isLoading, isAuthenticated, userType]);

  // Filter items based on enhanced permission checking - MEMOIZED
  const filteredItems = React.useMemo(() =>
    navItems.filter(hasPermissionForItem),
    [navItems, hasPermissionForItem]
  );

  // Debug logging in development - throttled to reduce console spam
  React.useEffect(() => {
    const isDevelopment = import.meta.env?.DEV || window.location.hostname === 'localhost';
    if (isDevelopment && userType && filteredItems.length > 0) {
      // Only log once per user session to reduce spam
      const debugKey = `sidebar-debug-${userType}`;
      if (!sessionStorage.getItem(debugKey)) {
        // Only log if debug mode is explicitly enabled
        const debugEnabled = localStorage.getItem('sidebar-debug-enabled') === 'true';
        if (debugEnabled) {
          console.log('🔍 UniversalSidebar navigation filtering debug:', {
            userType,
            totalNavItems: navItems.length,
            filteredItemsCount: filteredItems.length,
            filteredItemNames: filteredItems.map(item => item.name)
          });
        }
        sessionStorage.setItem(debugKey, 'logged');
      }
    }
  }, [userType]); // Only log when userType changes

  // Organize items by category for better navigation structure
  const organizeItemsByCategory = (items: NavItem[]) => {
    const categories = {
      main: items.filter(item => item.category === 'main'),
      content: items.filter(item => item.category === 'content'),
      ai: items.filter(item => item.category === 'ai'),
      system: items.filter(item => item.category === 'system'),
      security: items.filter(item => item.category === 'security'),
      super_admin: items.filter(item => item.category === 'super_admin')
    };

    return categories;
  };

  const categorizedItems = organizeItemsByCategory(filteredItems);

  // Permission filtering for troubleshooting in development
  React.useEffect(() => {
    // Debug info available in development mode only
  }, [userType, filteredItems.length, user, isAuthenticated]);



  const handleLogout = async () => {
    try {
      console.log('UniversalSidebar: Starting logout...');
      await dispatch(logout()).unwrap();
      console.log('UniversalSidebar: Logout successful, redirecting...');

      // Clear any remaining storage
      localStorage.clear();
      sessionStorage.clear();

      // Close sidebar if needed
      if (onClose) onClose();

      // Redirect to login page
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('UniversalSidebar: Logout failed:', error);
      // Even if logout fails, clear local state and redirect
      localStorage.clear();
      sessionStorage.clear();
      if (onClose) onClose();
      navigate('/login', { replace: true });
    }
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };



  const getRiskBadge = (riskLevel?: string) => {
    if (!riskLevel) return null;

    const colors = {
      low: 'bg-green-500/20 text-green-300 border border-green-500/30',
      medium: 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30',
      high: 'bg-orange-500/20 text-orange-300 border border-orange-500/30',
      critical: 'bg-red-500/20 text-red-300 border border-red-500/30'
    };

    const icons = {
      low: '🟢',
      medium: '🟡',
      high: '🟠',
      critical: '🔴'
    };

    return (
      <span className={`px-2 py-1 text-xs rounded-lg font-medium ${colors[riskLevel as keyof typeof colors]} flex items-center gap-1`}>
        <span>{icons[riskLevel as keyof typeof icons]}</span>
        {riskLevel.toUpperCase()}
      </span>
    );
  };

  const getUserTypeDisplay = () => {
    if (isSuperAdminType(userType)) {
      return { name: 'Super Administrator', color: 'text-red-600', icon: <User className="w-5 h-5" /> };
    }
    if (isAdminType(userType)) {
      return { name: 'Administrator', color: 'text-blue-600', icon: <User className="w-5 h-5" /> };
    }
    if (isMentorType(userType)) {
      return { name: 'Mentor', color: 'text-green-600', icon: <User className="w-5 h-5" /> };
    }
    if (isInvestorType(userType)) {
      return { name: 'Investor', color: 'text-purple-600', icon: <User className="w-5 h-5" /> };
    }
    if (userType === 'moderator') {
      return { name: 'Moderator', color: 'text-orange-600', icon: <User className="w-5 h-5" /> };
    }
    return { name: 'User', color: 'text-gray-600', icon: <User className="w-5 h-5" /> };
  };

  const userDisplay = getUserTypeDisplay();

  // Add keyboard support for closing mobile sidebar
  useEffect(() => {
    if (variant === 'mobile' && isOpen && onClose) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          onClose();
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [variant, isOpen, onClose]);

  const sidebarContent = (
    <div
      className={`flex flex-col h-full universal-sidebar ${isRTL ? 'rtl' : 'ltr'}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={isRTL ? { fontFamily: "'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif" } : {}}
      data-testid="sidebar"
    >
      {/* Header */}
      <div className={`sidebar-header flex items-center justify-between p-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* App Logo - Match login page styling */}
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="app-logo w-10 h-10 rounded-full flex items-center justify-center shadow-lg">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h1
                className="text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400"
                style={isRTL ? {
                  fontFamily: "'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                  letterSpacing: '0.025em'
                } : {}}
              >
                {t('app.name', 'Yasmeen AI')}
              </h1>
              <p
                className="text-xs text-white/70"
                style={isRTL ? {
                  fontFamily: "'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                } : {}}
              >
                {userDisplay.name}
              </p>
            </div>
          </div>
        </div>

        {/* User Info and Close Button */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`p-1.5 rounded-lg bg-gradient-to-br ${
            isSuperAdminType(userType) ? 'from-red-500 to-red-600' :
            isAdminType(userType) ? 'from-blue-500 to-blue-600' :
            isMentorType(userType) ? 'from-green-500 to-green-600' :
            isInvestorType(userType) ? 'from-purple-500 to-purple-600' :
            userType === 'moderator' ? 'from-orange-500 to-orange-600' :
            'from-gray-500 to-gray-600'
          }`}>
            {userDisplay.icon}
          </div>
          <div className={isRTL ? 'text-left' : 'text-right'}>
            <p className="text-xs text-white/90">{user?.username}</p>
            <p className="text-xs text-white/60">{userDisplay.name}</p>
          </div>

          {/* Close/Toggle Button - Always Visible */}
          {onToggle && (
            <button
              onClick={onToggle}
              className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors border border-white/20 ml-2 shadow-lg"
              aria-label={t('common.collapse', 'Collapse')}
              title={t('common.collapse', 'Collapse')}
            >
              <X className="w-4 h-4 text-white" />
            </button>
          )}
        </div>
        {variant === 'mobile' && onClose && (
          <button
            onClick={onClose}
            className="p-2 bg-red-600/80 hover:bg-red-600 rounded-lg transition-colors border border-white/20 shadow-lg"
            aria-label={t('common.close', 'Close')}
            title={t('common.close', 'Close')}
          >
            <X className="w-5 h-5 text-white" />
          </button>
        )}
      </div>

      {/* Navigation - Organized by Category */}
      <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
        <div className="space-y-6">
          {/* Main Navigation */}
          {categorizedItems.main.length > 0 && (
            <div className="space-y-2">
              {categorizedItems.main.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => handleNavigation(item.path, e)}
                  data-testid={`nav-${item.id}`}
                  className={`nav-item flex items-center justify-between p-4 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/80 hover:text-white'
                  }`}
                >
                  <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/70 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                    <span
                      className={`text-sm font-medium transition-all duration-300 ${isRTL ? 'text-right' : 'text-left'} ${
                        isActive(item.path) ? 'text-white font-semibold' : 'group-hover:text-white'
                      }`}
                      style={isRTL ? {
                        fontFamily: "'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                        letterSpacing: '0.025em',
                        lineHeight: '1.5'
                      } : {}}
                    >
                      {item.name}
                    </span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}

          {/* Content & Resources */}
          {categorizedItems.content.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider px-2">
                {t('navigation.content', 'Content & Resources')}
              </h3>
              {categorizedItems.content.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => handleNavigation(item.path, e)}
                  data-testid={`nav-${item.id}`}
                  className={`nav-item flex items-center justify-between p-3 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                    <span className={`text-sm font-medium transition-all duration-300 ${isRTL ? 'text-right' : 'text-left'} ${
                      isActive(item.path) ? 'text-white font-semibold' : 'group-hover:text-white'
                    }`}>{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}

          {/* AI Features */}
          {categorizedItems.ai.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider px-2">
                {t('navigation.ai', 'AI Features')}
              </h3>
              {categorizedItems.ai.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    if (onClose) onClose();
                  }}
                  className={`nav-item flex items-center justify-between p-3 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/70 hover:text-white'
                  }`}
                  data-testid="nav-item"
                >
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                    <span className={`text-sm font-medium ${isRTL ? 'text-right' : 'text-left'}`}>{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}

          {/* System & Admin */}
          {categorizedItems.system.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider px-2">
                {t('navigation.system', 'System & Admin')}
              </h3>
              {categorizedItems.system.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    if (onClose) onClose();
                  }}
                  className={`nav-item flex items-center justify-between p-3 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/70 hover:text-white'
                  }`}
                  data-testid="nav-item"
                >
                  <div className="flex items-center gap-3">
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                    <span className="text-sm font-medium">{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* User Profile & Logout */}
      <div className="border-t border-white/20 p-4">
        <button
          onClick={handleLogout}
          className={`flex items-center justify-center gap-3 w-full p-3 bg-gradient-to-r from-red-600/20 to-red-700/20 hover:from-red-600/30 hover:to-red-700/30 text-red-300 hover:text-red-200 rounded-xl transition-all duration-300 group border border-red-500/30 hover:border-red-400/50 hover:shadow-lg hover:shadow-red-500/20 ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          <LogOut className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
          <span className={`text-sm font-medium ${isRTL ? 'text-right' : 'text-left'}`}>{t('auth.logout', 'Logout')}</span>
        </button>
      </div>
    </div>
  );

  if (variant === 'mobile') {
    return (
      <>
        {/* Backdrop overlay */}
        <div
          className={`fixed inset-0 z-40 bg-black/60 backdrop-blur-sm transition-opacity duration-300 ${
            isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={onClose}
          aria-hidden="true"
        />
        {/* Sidebar panel */}
        <div className={`fixed top-0 ${isRTL ? 'right-0' : 'left-0'} z-50 h-full w-72 max-w-xs transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : (isRTL ? 'translate-x-full' : '-translate-x-full')
        }`}>
          <div className="flex flex-col h-full shadow-2xl">
            {sidebarContent}
          </div>
        </div>
      </>
    );
  }

  return (
    <div className={`hidden lg:flex lg:flex-shrink-0 transition-all duration-300 ease-in-out fixed ${isRTL ? 'right-0' : 'left-0'} top-0 h-full z-40 ${
      isCollapsed ? 'w-16' : 'w-72'
    }`}>
      <div className={`flex flex-col shadow-2xl h-full ${isCollapsed ? 'w-16' : 'w-72'}`}>
        {/* Always visible toggle button for desktop */}
        <button
          onClick={onToggle}
          className={`absolute ${isRTL ? 'left-2' : 'right-2'} top-2 z-10 p-2 bg-purple-600/80 hover:bg-purple-600 border border-white/30 rounded-lg transition-all duration-300 shadow-lg`}
          aria-label={isCollapsed ? t('common.expand') : t('common.collapse')}
          title={isCollapsed ? t('common.expand', 'Expand') : t('common.collapse', 'Collapse')}
          data-testid="sidebar-toggle"
        >
          {isCollapsed ? (
            isRTL ? <ChevronLeft className="w-4 h-4 text-white" /> : <ChevronRight className="w-4 h-4 text-white" />
          ) : (
            isRTL ? <ChevronRight className="w-4 h-4 text-white" /> : <ChevronLeft className="w-4 h-4 text-white" />
          )}
        </button>
        {isCollapsed ? (
          // Collapsed sidebar content
          <div className={`flex flex-col h-full universal-sidebar ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
            {/* Collapsed header */}
            <div className="sidebar-header flex items-center justify-center p-4">
              <div className="app-logo w-12 h-12 rounded-full flex items-center justify-center shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
            </div>

            {/* Collapsed navigation - All items in simple list */}
            <div className="flex-1 overflow-y-auto p-2 custom-scrollbar">
              <div className="space-y-1">
                {/* Main items first */}
                {categorizedItems.main.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                    data-testid="nav-item"
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                  </a>
                ))}

                {/* Separator for content items */}
                {categorizedItems.content.length > 0 && (
                  <div className="h-px bg-white/20 my-2"></div>
                )}

                {/* Content items */}
                {categorizedItems.content.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                    data-testid="nav-item"
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                  </a>
                ))}

                {/* AI and System items */}
                {(categorizedItems.ai.length > 0 || categorizedItems.system.length > 0) && (
                  <div className="h-px bg-white/20 my-2"></div>
                )}

                {categorizedItems.ai.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                    data-testid="nav-item"
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                  </a>
                ))}

                {categorizedItems.system.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                    data-testid="nav-item"
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {getIconComponent(item.icon)}
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Collapsed logout */}
            <div className="p-2 border-t border-white/20">
              <button
                onClick={handleLogout}
                className="w-full flex items-center justify-center p-3 text-white/70 hover:bg-red-500/20 hover:text-red-300 rounded-xl transition-all duration-200 glass-light border border-white/20 hover:border-red-500/50"
                title={t('auth.logout')}
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        ) : (
          sidebarContent
        )}
      </div>
    </div>
  );
});

// Add display name for debugging
UniversalSidebar.displayName = 'UniversalSidebar';

export default UniversalSidebar;

import { apiClient } from './apiClient';
import { apiCache, cacheKeys } from '../utils/apiCache';

// Analytics data interfaces
export interface AnalyticsOverview {
  total_users: number;
  total_business_ideas: number;
  total_funding_raised: number;
  active_mentorships: number;
  completed_milestones: number;
  success_rate: number;
}

export interface AnalyticsGrowth {
  user_growth: number;
  idea_growth: number;
  funding_growth: number;
  mentorship_growth: number;
}

export interface AnalyticsCategory {
  name: string;
  count: number;
  percentage: number;
}

export interface AnalyticsActivity {
  type: string;
  description: string;
  timestamp: string;
  user: string;
}

export interface AnalyticsData {
  overview: AnalyticsOverview;
  growth: AnalyticsGrowth;
  categories: AnalyticsCategory[];
  recent_activities: AnalyticsActivity[];
}

export interface UserAnalytics {
  user_id: number;
  total_ideas: number;
  total_funding: number;
  mentorship_sessions: number;
  completed_milestones: number;
  success_rate: number;
  activity_score: number;
}

export interface BusinessIdeaAnalytics {
  idea_id: number;
  views: number;
  likes: number;
  comments: number;
  funding_received: number;
  milestone_progress: number;
  success_probability: number;
}

// Analytics API service
export const analyticsAPI = {
  // Get general analytics data
  async getAnalytics(timeRange: string = '30d'): Promise<AnalyticsData> {
    return apiCache.get(
      cacheKeys.analytics(timeRange),
      async () => {
        try {
          const response = await apiClient.get(`/analytics/overview/?time_range=${timeRange}`);
          return response.data;
        } catch (error) {
          console.error('Error fetching analytics:', error);
          // ✅ REAL DATA: Throw error instead of returning mock data
          throw new Error(`Failed to fetch analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
      },
      5 // Cache analytics for 5 minutes
    );
  },

  // Get user-specific analytics
  async getUserAnalytics(userId: number, timeRange: string = '30d'): Promise<UserAnalytics> {
    try {
      const response = await apiClient.get(`/analytics/users/${userId}/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      // ✅ REAL DATA: Throw error instead of returning mock data
      throw new Error(`Failed to fetch user analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Get real-time analytics
  async getRealTimeAnalytics(): Promise<any> {
    try {
      const response = await apiClient.get('/analytics/real-time/');
      return response.data;
    } catch (error) {
      console.error('Error fetching real-time analytics:', error);
      // Return empty data structure on error
      return {
        activeUsers: 0,
        pageViews: 0,
        sessionDuration: 0,
        bounceRate: 0,
        conversionRate: 0,
        topPages: [],
        userFlow: [],
        deviceBreakdown: { desktop: 0, mobile: 0, tablet: 0 },
        geographicData: [],
        realTimeEvents: []
      };
    }
  },

  // Get dashboard analytics
  async getDashboardAnalytics(): Promise<any> {
    try {
      const response = await apiClient.get('/analytics/dashboard/');
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      return {
        total_users: 150,
        active_users: 120,
        total_posts: 45,
        total_events: 12,
        growth_rate: 15.5,
        engagement_rate: 78.2
      };
    }
  },

  // Get business idea analytics
  async getBusinessIdeaAnalytics(ideaId: number, timeRange: string = '30d'): Promise<BusinessIdeaAnalytics> {
    try {
      const response = await apiClient.get(`/analytics/business-ideas/${ideaId}/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching business idea analytics:', error);
      // ✅ REAL DATA: Throw error instead of returning hardcoded fallback data
      throw new Error(`Failed to fetch business idea analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Get platform metrics
  async getPlatformMetrics(timeRange: string = '30d'): Promise<any> {
    return apiCache.get(
      cacheKeys.platformMetrics(timeRange),
      async () => {
        try {
          const response = await apiClient.get(`/analytics/platform/?time_range=${timeRange}`);
          return response.data;
        } catch (error) {
          console.error('Error fetching platform metrics:', error);
          // ✅ REAL DATA: Throw error instead of returning mock data
          throw new Error(`Failed to fetch platform metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },
      5 // Cache platform metrics for 5 minutes
    );
  },

  // Get funding analytics
  async getFundingAnalytics(timeRange: string = '30d'): Promise<any> {
    try {
      const response = await apiClient.get(`/analytics/funding/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching funding analytics:', error);
      // ✅ REAL DATA: Throw error instead of returning mock data
      throw new Error(`Failed to fetch funding analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Get mentorship analytics
  async getMentorshipAnalytics(timeRange: string = '30d'): Promise<any> {
    try {
      const response = await apiClient.get(`/analytics/mentorship/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching mentorship analytics:', error);
      // ✅ REAL DATA: Throw error instead of returning mock data
      throw new Error(`Failed to fetch mentorship analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Get category analytics
  async getCategoryAnalytics(timeRange: string = '30d'): Promise<AnalyticsCategory[]> {
    try {
      const response = await apiClient.get(`/analytics/categories/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching category analytics:', error);
      // ✅ REAL DATA: Throw error instead of returning mock data
      throw new Error(`Failed to fetch category analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Get activity feed
  async getActivityFeed(limit: number = 10, timeRange: string = '30d'): Promise<AnalyticsActivity[]> {
    try {
      const response = await apiClient.get(`/analytics/activities/?limit=${limit}&time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching activity feed:', error);
      // ✅ REAL DATA: Throw error instead of returning mock data
      throw new Error(`Failed to fetch activity feed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Export analytics data
  async exportAnalytics(format: 'csv' | 'xlsx' | 'pdf', timeRange: string = '30d'): Promise<Blob> {
    try {
      const response = await apiClient.get(`/analytics/export/?format=${format}&time_range=${timeRange}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting analytics:', error);
      // ✅ REAL DATA: Throw error instead of returning mock data
      throw new Error(`Failed to export analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
};

export default analyticsAPI;

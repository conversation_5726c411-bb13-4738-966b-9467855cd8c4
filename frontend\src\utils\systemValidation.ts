/**
 * SYSTEM VALIDATION UTILITY
 * Comprehensive testing and validation of the role system, routing, and navigation
 * This utility helps identify any remaining issues after the fixes
 */

import { User } from '../services/api';
import {
  getUserRoles,
  getUserPermissions,
  canAccessRoute,
  getUserType,
  UserRole,
  PermissionLevel
} from './unifiedRoleManager';
import { allRoutes } from '../routes/consolidatedRoutes';
import { RouteConfig } from '../routes/routeConfig';

export interface ValidationResult {
  success: boolean;
  issues: ValidationIssue[];
  summary: ValidationSummary;
}

export interface ValidationIssue {
  type: 'error' | 'warning' | 'info';
  category: 'role' | 'route' | 'navigation' | 'permission';
  message: string;
  details?: any;
}

export interface ValidationSummary {
  totalRoutes: number;
  accessibleRoutes: Record<UserRole, number>;
  roleConsistency: boolean;
  navigationConsistency: boolean;
  criticalIssues: number;
  warnings: number;
}

/**
 * Create test users for each role
 */
export function createTestUsers(): Record<UserRole, User> {
  const baseUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    profile: null
  };

  return {
    user: {
      ...baseUser,
      username: 'regular_user',
      is_superuser: false,
      is_admin: false,
      is_staff: false
    },
    moderator: {
      ...baseUser,
      username: 'moderator_user',
      is_superuser: false,
      is_admin: false,
      is_staff: true
    },
    mentor: {
      ...baseUser,
      username: 'mentor_user',
      is_superuser: false,
      is_admin: false,
      is_staff: false,
      profile: {
        primary_role: { name: 'mentor' },
        active_roles: [{ name: 'mentor' }]
      }
    },
    investor: {
      ...baseUser,
      username: 'investor_user',
      is_superuser: false,
      is_admin: false,
      is_staff: false,
      profile: {
        primary_role: { name: 'investor' },
        active_roles: [{ name: 'investor' }]
      }
    },
    admin: {
      ...baseUser,
      username: 'admin_user',
      is_superuser: false,
      is_admin: true,
      is_staff: true
    },
    super_admin: {
      ...baseUser,
      username: 'super_admin_user',
      is_superuser: true,
      is_admin: true,
      is_staff: true
    }
  };
}

/**
 * Quick test to verify role system is working
 */
export function quickRoleTest(): { success: boolean; issues: string[] } {
  const issues: string[] = [];
  const testUsers = createTestUsers();

  // Test 1: Super admin should have super_admin role
  const superAdminRoles = getUserRoles(testUsers.super_admin);
  if (!superAdminRoles.includes('super_admin')) {
    issues.push('Super admin user does not have super_admin role');
  }

  // Test 2: Admin should have admin role
  const adminRoles = getUserRoles(testUsers.admin);
  if (!adminRoles.includes('admin')) {
    issues.push('Admin user does not have admin role');
  }

  // Test 3: Moderator should have moderator role
  const moderatorRoles = getUserRoles(testUsers.moderator);
  if (!moderatorRoles.includes('moderator')) {
    issues.push('Moderator user does not have moderator role');
  }

  // Test 4: Super admin should be able to access everything
  const superAdminCanAccess = canAccessRoute(testUsers.super_admin, ['admin'], ['admin'], true);
  if (!superAdminCanAccess) {
    issues.push('Super admin cannot access admin routes');
  }

  // Test 5: Regular user should not access admin routes
  const userCannotAccessAdmin = !canAccessRoute(testUsers.user, ['admin'], ['admin'], true);
  if (!userCannotAccessAdmin) {
    issues.push('Regular user can access admin routes (should not be able to)');
  }

  return {
    success: issues.length === 0,
    issues
  };
}

/**
 * Test role determination for all user types
 */
export function testRoleDetermination(): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  const testUsers = createTestUsers();

  Object.entries(testUsers).forEach(([expectedRole, user]) => {
    const actualRoles = getUserRoles(user);
    const primaryRole = getUserType(user);
    const permissions = getUserPermissions(user);

    // Check if the expected role is present
    if (!actualRoles.includes(expectedRole as UserRole)) {
      issues.push({
        type: 'error',
        category: 'role',
        message: `User ${user.username} should have role '${expectedRole}' but got: ${actualRoles.join(', ')}`,
        details: { expectedRole, actualRoles, user }
      });
    }

    // Check if primary role is correct
    if (primaryRole !== expectedRole) {
      issues.push({
        type: 'warning',
        category: 'role',
        message: `User ${user.username} primary role should be '${expectedRole}' but got: ${primaryRole}`,
        details: { expectedRole, primaryRole, user }
      });
    }

    // Check if permissions are appropriate
    if (permissions.length === 0) {
      issues.push({
        type: 'error',
        category: 'permission',
        message: `User ${user.username} has no permissions`,
        details: { user, permissions }
      });
    }
  });

  return issues;
}

/**
 * Test route access for all user types
 */
export function testRouteAccess(): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  const testUsers = createTestUsers();

  Object.entries(testUsers).forEach(([roleName, user]) => {
    const userRole = roleName as UserRole;
    let accessibleCount = 0;

    allRoutes.forEach(route => {
      const hasAccess = canAccessRoute(
        user,
        route.roles,
        route.permissions,
        route.requireAuth !== false
      );

      if (hasAccess) {
        accessibleCount++;
      }

      // Super admin should access everything
      if (userRole === 'super_admin' && !hasAccess) {
        issues.push({
          type: 'error',
          category: 'route',
          message: `Super admin should access all routes but cannot access: ${route.path}`,
          details: { route, user }
        });
      }

      // Check if role-specific routes are accessible
      if (route.roles?.includes(userRole) && !hasAccess) {
        issues.push({
          type: 'error',
          category: 'route',
          message: `User with role '${userRole}' should access route ${route.path} but cannot`,
          details: { route, user, userRole }
        });
      }
    });

    // Log accessible route count for analysis
    issues.push({
      type: 'info',
      category: 'route',
      message: `User '${userRole}' can access ${accessibleCount}/${allRoutes.length} routes`,
      details: { userRole, accessibleCount, totalRoutes: allRoutes.length }
    });
  });

  return issues;
}

/**
 * Test dashboard routing for all user types
 */
export function testDashboardRouting(): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  const testUsers = createTestUsers();

  const expectedDashboards: Record<UserRole, string> = {
    user: '/dashboard',
    moderator: '/dashboard/moderator',
    mentor: '/dashboard/mentor',
    investor: '/dashboard/investor',
    admin: '/admin',
    super_admin: '/super_admin'
  };

  Object.entries(testUsers).forEach(([roleName, user]) => {
    const userRole = roleName as UserRole;
    const dashboardRoute = getDashboardRoute(user);
    const expectedRoute = expectedDashboards[userRole];

    if (dashboardRoute !== expectedRoute) {
      issues.push({
        type: 'error',
        category: 'navigation',
        message: `User '${userRole}' dashboard route should be '${expectedRoute}' but got: ${dashboardRoute}`,
        details: { userRole, expected: expectedRoute, actual: dashboardRoute }
      });
    }
  });

  return issues;
}

/**
 * Validate that all navigation paths correspond to actual routes
 */
export function validateNavigationConsistency(): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  
  // Common navigation paths from the sidebar
  const navigationPaths = [
    '/dashboard',
    '/dashboard/business-ideas',
    '/dashboard/business-plans',
    '/dashboard/templates',
    '/dashboard/posts',
    '/dashboard/events',
    '/dashboard/resources',
    '/dashboard/analytics',
    '/dashboard/incubator',
    '/dashboard/moderator',
    '/dashboard/mentor',
    '/dashboard/investor',
    '/dashboard/mentorship/sessions',
    '/dashboard/mentorship/mentees',
    '/dashboard/investments',
    '/dashboard/investments/portfolio',
    '/dashboard/moderation/content',
    '/dashboard/moderation/reports',
    '/admin',
    '/super_admin',
    '/chat/enhanced',
    '/settings'
  ];

  const routePaths = allRoutes.map(route => route.path);

  navigationPaths.forEach(navPath => {
    if (!routePaths.includes(navPath)) {
      issues.push({
        type: 'warning',
        category: 'navigation',
        message: `Navigation path '${navPath}' does not correspond to any defined route`,
        details: { navPath, availableRoutes: routePaths }
      });
    }
  });

  return issues;
}

/**
 * Run comprehensive system validation
 */
export function validateSystem(): ValidationResult {
  const issues: ValidationIssue[] = [];

  // Run all validation tests
  issues.push(...testRoleDetermination());
  issues.push(...testRouteAccess());
  issues.push(...testDashboardRouting());
  issues.push(...validateNavigationConsistency());

  // Calculate summary
  const criticalIssues = issues.filter(issue => issue.type === 'error').length;
  const warnings = issues.filter(issue => issue.type === 'warning').length;
  
  const accessibleRoutes: Record<UserRole, number> = {} as Record<UserRole, number>;
  issues.filter(issue => issue.type === 'info' && issue.category === 'route')
    .forEach(issue => {
      if (issue.details?.userRole && issue.details?.accessibleCount) {
        accessibleRoutes[issue.details.userRole as UserRole] = issue.details.accessibleCount;
      }
    });

  const summary: ValidationSummary = {
    totalRoutes: allRoutes.length,
    accessibleRoutes,
    roleConsistency: criticalIssues === 0,
    navigationConsistency: issues.filter(i => i.category === 'navigation' && i.type === 'error').length === 0,
    criticalIssues,
    warnings
  };

  return {
    success: criticalIssues === 0,
    issues,
    summary
  };
}

/**
 * Generate a human-readable validation report
 */
export function generateValidationReport(result: ValidationResult): string {
  const { success, issues, summary } = result;
  
  let report = `
# SYSTEM VALIDATION REPORT
Generated: ${new Date().toISOString()}

## Summary
- **Status**: ${success ? '✅ PASSED' : '❌ FAILED'}
- **Total Routes**: ${summary.totalRoutes}
- **Critical Issues**: ${summary.criticalIssues}
- **Warnings**: ${summary.warnings}
- **Role Consistency**: ${summary.roleConsistency ? '✅' : '❌'}
- **Navigation Consistency**: ${summary.navigationConsistency ? '✅' : '❌'}

## Route Access by Role
`;

  Object.entries(summary.accessibleRoutes).forEach(([role, count]) => {
    const percentage = ((count / summary.totalRoutes) * 100).toFixed(1);
    report += `- **${role}**: ${count}/${summary.totalRoutes} routes (${percentage}%)\n`;
  });

  if (issues.length > 0) {
    report += `\n## Issues Found\n`;
    
    const errors = issues.filter(i => i.type === 'error');
    const warnings = issues.filter(i => i.type === 'warning');
    
    if (errors.length > 0) {
      report += `\n### Critical Errors (${errors.length})\n`;
      errors.forEach((issue, index) => {
        report += `${index + 1}. **${issue.category.toUpperCase()}**: ${issue.message}\n`;
      });
    }
    
    if (warnings.length > 0) {
      report += `\n### Warnings (${warnings.length})\n`;
      warnings.forEach((issue, index) => {
        report += `${index + 1}. **${issue.category.toUpperCase()}**: ${issue.message}\n`;
      });
    }
  }

  return report;
}

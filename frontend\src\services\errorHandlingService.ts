/**
 * Centralized Error Handling Service
 * Provides consistent error handling, user-friendly messages, and retry mechanisms
 */

export interface ErrorDetails {
  type: 'network' | 'auth' | 'validation' | 'server' | 'rate_limit' | 'unknown';
  message: string;
  userMessage: string;
  retryable: boolean;
  statusCode?: number;
  originalError?: any;
  requestId?: string;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export class ErrorHandlingService {
  private static defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  };

  /**
   * Classify and process an error into a standardized format
   */
  static processError(error: any): ErrorDetails {
    let errorDetails: ErrorDetails = {
      type: 'unknown',
      message: 'An unknown error occurred',
      userMessage: 'Something went wrong. Please try again.',
      retryable: true,
      originalError: error,
    };

    // Extract error information
    if (error?.response) {
      // HTTP response error
      const status = error.response.status;
      const data = error.response.data;
      
      errorDetails.statusCode = status;
      errorDetails.message = data?.message || data?.error || error.message || 'HTTP Error';
      errorDetails.requestId = data?.request_id;

      // Classify by status code
      if (status === 401 || status === 403) {
        errorDetails.type = 'auth';
        errorDetails.userMessage = 'Authentication required. Please log in again.';
        errorDetails.retryable = false;
      } else if (status === 400) {
        errorDetails.type = 'validation';
        errorDetails.userMessage = data?.message || 'Invalid input. Please check your data.';
        errorDetails.retryable = false;
      } else if (status === 429) {
        errorDetails.type = 'rate_limit';
        errorDetails.userMessage = 'Too many requests. Please wait a moment and try again.';
        errorDetails.retryable = true;
      } else if (status >= 500) {
        errorDetails.type = 'server';
        errorDetails.userMessage = 'Server error. Please try again in a moment.';
        errorDetails.retryable = true;
      } else if (status >= 400) {
        errorDetails.type = 'validation';
        errorDetails.userMessage = data?.message || 'Request failed. Please check your input.';
        errorDetails.retryable = false;
      }

      // Check for specific error types in the response
      if (data?.error_type) {
        errorDetails.type = data.error_type;
      }

    } else if (error?.request) {
      // Network error (no response received)
      errorDetails.type = 'network';
      errorDetails.message = 'Network error - no response received';
      errorDetails.userMessage = 'Network error. Please check your connection and try again.';
      errorDetails.retryable = true;
    } else if (error?.message) {
      // JavaScript error or other error with message
      errorDetails.message = error.message;
      
      // Check for specific error patterns
      if (error.message.includes('timeout')) {
        errorDetails.type = 'network';
        errorDetails.userMessage = 'Request timed out. Please try again.';
      } else if (error.message.includes('Network Error')) {
        errorDetails.type = 'network';
        errorDetails.userMessage = 'Network error. Please check your connection.';
      }
    }

    return errorDetails;
  }

  /**
   * Get user-friendly error message based on error type
   * ✅ REAL DATA: Use translation keys instead of hardcoded messages
   */
  static getUserMessage(errorDetails: ErrorDetails, t?: (key: string, fallback?: string) => string): string {
    // If translation function is provided, use translation keys
    if (t) {
      const messageKeys = {
        network: t('errors.networkError', 'Network error. Please check your connection and try again.'),
        auth: t('errors.authenticationError', 'Authentication required. Please log in again.'),
        validation: t('errors.validationError', 'Invalid input. Please check your data and try again.'),
        server: t('errors.serverError', 'Server error. Please try again in a moment.'),
        rate_limit: t('errors.rateLimitError', 'Too many requests. Please wait a moment and try again.'),
        unknown: t('errors.unknownError', 'Something went wrong. Please try again.'),
      };

      return errorDetails.userMessage || messageKeys[errorDetails.type] || messageKeys.unknown;
    }

    // Fallback to hardcoded messages if no translation function provided
    const messages = {
      network: 'Network error. Please check your connection and try again.',
      auth: 'Authentication required. Please log in again.',
      validation: 'Invalid input. Please check your data and try again.',
      server: 'Server error. Please try again in a moment.',
      rate_limit: 'Too many requests. Please wait a moment and try again.',
      unknown: 'Something went wrong. Please try again.',
    };

    return errorDetails.userMessage || messages[errorDetails.type] || messages.unknown;
  }

  /**
   * Determine if an error is retryable
   */
  static isRetryable(errorDetails: ErrorDetails): boolean {
    const retryableTypes = ['network', 'server', 'rate_limit'];
    return errorDetails.retryable && retryableTypes.includes(errorDetails.type);
  }

  /**
   * Calculate delay for retry attempt
   */
  static calculateRetryDelay(attempt: number, config: RetryConfig = this.defaultRetryConfig): number {
    const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    return Math.min(delay, config.maxDelay);
  }

  /**
   * Execute a function with retry logic
   */
  static async withRetry<T>(
    fn: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    let lastError: any;

    for (let attempt = 1; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        const errorDetails = this.processError(error);

        // Don't retry if error is not retryable
        if (!this.isRetryable(errorDetails)) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === retryConfig.maxRetries) {
          break;
        }

        // Wait before retrying
        const delay = this.calculateRetryDelay(attempt, retryConfig);
        console.log(`Retry attempt ${attempt} failed, waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Create a fallback response for when services are unavailable
   */
  static createFallbackResponse(type: 'chat' | 'analysis' | 'status'): any {
    const fallbacks = {
      chat: {
        success: false,
        message: "I'm temporarily unavailable. Please try again in a moment.",
        fallback: true,
      },
      analysis: {
        success: false,
        message: "Analysis service is temporarily unavailable. Please try again later.",
        fallback: true,
      },
      status: {
        available: false,
        service: 'offline',
        message: 'Service status unavailable',
        fallback: true,
      },
    };

    return fallbacks[type] || fallbacks.chat;
  }

  /**
   * Log error for debugging and monitoring
   */
  static logError(errorDetails: ErrorDetails, context?: string): void {
    const logData = {
      type: errorDetails.type,
      message: errorDetails.message,
      statusCode: errorDetails.statusCode,
      requestId: errorDetails.requestId,
      context,
      timestamp: new Date().toISOString(),
    };

    console.error('Error logged:', logData);
    
    // In production, you might want to send this to a monitoring service
    // Example: sendToMonitoringService(logData);
  }
}

export default ErrorHandlingService;

/**
 * Dashboard Consolidation Validation Script
 * Validates that the dashboard consolidation is working correctly
 */

import { getUserRole } from '../../utils/unifiedRoleManager';
import { dashboardDataService } from '../../services/dashboardDataService';
import { DashboardRole } from '../../types/dashboard';

interface ValidationResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

class DashboardConsolidationValidator {
  private results: ValidationResult[] = [];

  /**
   * Run all validation tests
   */
  async validate(): Promise<ValidationResult[]> {
    console.log('🔍 Starting Dashboard Consolidation Validation...\n');

    // Test role detection
    await this.testRoleDetection();
    
    // Test data service
    await this.testDataService();
    
    // Test provider factory
    await this.testProviderFactory();
    
    // Test unified hooks
    await this.testUnifiedHooks();

    // Print results
    this.printResults();
    
    return this.results;
  }

  /**
   * Test role detection functionality
   */
  private async testRoleDetection(): Promise<void> {
    console.log('Testing role detection...');

    const testCases = [
      { user: null, expected: 'user' },
      { user: { id: '1', username: 'user', role: 'user' }, expected: 'user' },
      { user: { id: '2', username: 'admin', role: 'admin' }, expected: 'admin' },
      { user: { id: '3', username: 'superadmin', role: 'super_admin' }, expected: 'super_admin' },
      { user: { id: '4', username: 'moderator', role: 'moderator' }, expected: 'moderator' },
      { user: { id: '5', username: 'mentor', role: 'mentor' }, expected: 'mentor' },
      { user: { id: '6', username: 'investor', role: 'investor' }, expected: 'investor' },
    ];

    for (const testCase of testCases) {
      try {
        const result = getUserRole(testCase.user as any);
        const passed = result === testCase.expected;
        
        this.results.push({
          test: `Role Detection: ${testCase.user?.role || 'null'} → ${testCase.expected}`,
          passed,
          message: passed 
            ? `✅ Correctly identified role as ${result}`
            : `❌ Expected ${testCase.expected}, got ${result}`,
          details: { input: testCase.user, output: result }
        });
      } catch (error) {
        this.results.push({
          test: `Role Detection: ${testCase.user?.role || 'null'}`,
          passed: false,
          message: `❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: { error }
        });
      }
    }
  }

  /**
   * Test dashboard data service
   */
  private async testDataService(): Promise<void> {
    console.log('Testing dashboard data service...');

    const roles: DashboardRole[] = ['user', 'admin', 'super_admin', 'moderator', 'mentor', 'investor'];
    
    for (const role of roles) {
      try {
        const mockUser = { id: '1', username: 'test', role };
        const data = await dashboardDataService.getDashboardData(mockUser as any);
        
        const hasStats = Array.isArray(data.stats) && data.stats.length > 0;
        const hasQuickActions = Array.isArray(data.quickActions) && data.quickActions.length > 0;
        const passed = hasStats && hasQuickActions;
        
        this.results.push({
          test: `Data Service: ${role} data`,
          passed,
          message: passed 
            ? `✅ Successfully fetched data for ${role} (${data.stats.length} stats, ${data.quickActions.length} actions)`
            : `❌ Failed to fetch complete data for ${role}`,
          details: {
            statsCount: data.stats.length,
            actionsCount: data.quickActions.length,
            hasAdditionalData: !!data.additionalData
          }
        });
      } catch (error) {
        this.results.push({
          test: `Data Service: ${role} data`,
          passed: false,
          message: `❌ Error fetching data for ${role}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: { error }
        });
      }
    }
  }

  /**
   * Test provider factory
   */
  private async testProviderFactory(): Promise<void> {
    console.log('Testing provider factory...');

    // Provider factory has been removed - now using Redux
    this.results.push({
      test: 'Provider Factory: Migration to Redux',
      passed: true,
      message: '✅ Dashboard providers migrated to Redux state management',
      details: {
        migration: 'Context API providers replaced with Redux slices',
        newApproach: 'Using dashboardSlice for state management'
      }
    });

    // Individual providers have been removed - now using Redux
    const providerNames = [
      'UserDashboardProvider',
      'AdminDashboardProvider',
      'SuperAdashboardProvider',
      'ModeratorDashboardProvider'
    ];

    for (const providerName of providerNames) {
      this.results.push({
        test: `Provider Migration: ${providerName}`,
        passed: true,
        message: `✅ ${providerName} migrated to Redux`,
        details: {
          providerName,
          migration: 'Replaced with Redux dashboardSlice',
          newApproach: 'Role-based state management in Redux store'
        }
      });
    }
  }

  /**
   * Test unified hooks
   */
  private async testUnifiedHooks(): Promise<void> {
    console.log('Testing unified hooks...');

    try {
      // Test hook imports
      const { useUnifiedDashboardData, useBusinessIdeas, useDashboardData } = await import('../../hooks/useUnifiedDashboardData');
      
      this.results.push({
        test: 'Unified Hooks: Module imports',
        passed: true,
        message: '✅ Unified hooks imported successfully',
        details: {
          hasUnifiedHook: !!useUnifiedDashboardData,
          hasBusinessIdeasHook: !!useBusinessIdeas,
          hasDashboardDataHook: !!useDashboardData
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Unified Hooks: Module imports',
        passed: false,
        message: `❌ Failed to import unified hooks: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      });
    }
  }

  /**
   * Print validation results
   */
  private printResults(): void {
    console.log('\n📊 Validation Results:');
    console.log('='.repeat(50));

    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);

    console.log(`\n📈 Overall Score: ${passed}/${total} (${percentage}%)\n`);

    // Group results by category
    const categories = new Map<string, ValidationResult[]>();
    
    this.results.forEach(result => {
      const category = result.test.split(':')[0];
      if (!categories.has(category)) {
        categories.set(category, []);
      }
      categories.get(category)!.push(result);
    });

    // Print results by category
    categories.forEach((results, category) => {
      console.log(`\n🔍 ${category}:`);
      results.forEach(result => {
        console.log(`  ${result.message}`);
      });
    });

    // Print failed tests details
    const failed = this.results.filter(r => !r.passed);
    if (failed.length > 0) {
      console.log('\n❌ Failed Tests Details:');
      failed.forEach(result => {
        console.log(`\n  Test: ${result.test}`);
        console.log(`  Message: ${result.message}`);
        if (result.details) {
          console.log(`  Details:`, result.details);
        }
      });
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    if (percentage >= 90) {
      console.log('🎉 Dashboard consolidation validation PASSED!');
    } else if (percentage >= 70) {
      console.log('⚠️  Dashboard consolidation validation PARTIALLY PASSED - some issues need attention');
    } else {
      console.log('❌ Dashboard consolidation validation FAILED - significant issues found');
    }
    console.log('='.repeat(50));
  }
}

/**
 * Run validation if this file is executed directly
 */
if (require.main === module) {
  const validator = new DashboardConsolidationValidator();
  validator.validate().catch(console.error);
}

export { DashboardConsolidationValidator };
export default DashboardConsolidationValidator;

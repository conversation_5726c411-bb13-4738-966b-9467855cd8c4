import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Calendar,
  Clock,
  Video,
  Users,
  Plus,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Star,
  MessageSquare
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';
import { apiClient } from '../../services/api';

const MentorshipCalendarPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not a mentor
  useEffect(() => {
    if (isAuthenticated && userRole !== 'mentor') {
      console.warn('Access denied: User is not a mentor');
      navigate('/dashboard');
    }
  }, [isAuthenticated, userRole, navigate]);

  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');

  // ✅ REAL DATA: Sessions from API
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch mentorship sessions from API
  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setLoading(true);

        // ✅ REAL DATA: Fetch mentorship sessions from API
        const response = await apiClient.get('/api/incubator/mentorship-sessions/');
        const sessionsData = response.data.results || [];

        setSessions(sessionsData);

      } catch (error) {
        console.error('Failed to fetch mentorship sessions:', error);

        // ✅ REAL DATA: Show error state instead of mock data
        setSessions([]);

        // TODO: Show user-friendly error message
        // toast.error(t('errors.failedToLoadSessions'));

      } finally {
        setLoading(false);
      }
    };

    fetchSessions();
  }, []);

  // Calendar helper functions
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getSessionsForDate = (date: Date) => {
    return sessions.filter(session => {
      const sessionDate = new Date(session.startTime);
      return sessionDate.toDateString() === date.toDateString();
    });
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const renderCalendarGrid = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const days = [];

    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div key={`empty-${i}`} className="h-24 border border-white/10"></div>
      );
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
      const daySessions = getSessionsForDate(date);
      const isToday = date.toDateString() === new Date().toDateString();
      const isSelected = date.toDateString() === selectedDate.toDateString();

      days.push(
        <div
          key={day}
          onClick={() => setSelectedDate(date)}
          className={`h-24 border border-white/10 p-2 cursor-pointer hover:bg-white/5 transition-colors ${
            isToday ? 'bg-purple-600/20' : ''
          } ${isSelected ? 'ring-2 ring-purple-500' : ''}`}
        >
          <div className={`text-sm font-medium mb-1 ${isToday ? 'text-purple-400' : 'text-white'}`}>
            {day}
          </div>
          <div className="space-y-1">
            {daySessions.slice(0, 2).map(session => (
              <div
                key={session.id}
                className={`text-xs p-1 rounded text-white truncate ${session.color}`}
              >
                {formatTime(session.startTime)} - {session.mentee.name}
              </div>
            ))}
            {daySessions.length > 2 && (
              <div className="text-xs text-gray-400">
                +{daySessions.length - 2} more
              </div>
            )}
          </div>
        </div>
      );
    }

    return days;
  };

  // Don't render if not authenticated or not a mentor
  if (!isAuthenticated || userRole !== 'mentor') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-300">This page is only accessible to mentors.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white">Mentorship Calendar</h1>
            <p className="text-gray-300 mt-1">
              Manage your mentorship sessions and availability
            </p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <Link
              to="/dashboard/mentorship/availability"
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center"
            >
              <Clock size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Set Availability
            </Link>
            <Link
              to="/dashboard/mentorship/sessions/schedule"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center"
            >
              <Plus size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Schedule Session
            </Link>
          </div>
        </div>

        {/* Calendar Navigation */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => navigateMonth('prev')}
              className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <ChevronLeft size={20} />
            </button>
            <h2 className="text-xl font-semibold text-white min-w-[200px] text-center">
              {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </h2>
            <button
              onClick={() => navigateMonth('next')}
              className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <ChevronRight size={20} />
            </button>
          </div>

          <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            {['month', 'week', 'day'].map(mode => (
              <button
                key={mode}
                onClick={() => setViewMode(mode as any)}
                className={`px-3 py-1 rounded text-sm capitalize ${
                  viewMode === mode 
                    ? 'bg-purple-600 text-white' 
                    : 'bg-white/10 text-gray-300 hover:bg-white/20'
                }`}
              >
                {mode}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Calendar Grid */}
          <div className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
              {/* Calendar Header */}
              <div className="grid grid-cols-7 bg-white/5">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="p-3 text-center text-gray-300 font-medium border-r border-white/10 last:border-r-0">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Body */}
              <div className="grid grid-cols-7">
                {renderCalendarGrid()}
              </div>
            </div>
          </div>

          {/* Selected Date Sessions */}
          <div className="lg:col-span-1">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">
                {formatDate(selectedDate)}
              </h3>

              <div className="space-y-3">
                {getSessionsForDate(selectedDate).map(session => (
                  <div key={session.id} className="bg-white/10 rounded-lg p-3 border border-white/20">
                    <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-3 h-3 rounded-full mr-2 ${session.color} ${isRTL ? "ml-2 mr-0" : ""}`}></div>
                      <span className="text-white font-medium text-sm">{session.title}</span>
                    </div>
                    <p className="text-gray-300 text-sm mb-1">{session.mentee.name}</p>
                    <p className="text-gray-400 text-xs">
                      {formatTime(session.startTime)} - {formatTime(session.endTime)}
                    </p>
                    <div className={`flex space-x-2 mt-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <button className="text-purple-400 hover:text-purple-300 text-xs">
                        Join
                      </button>
                      <button className="text-gray-400 hover:text-gray-300 text-xs">
                        Reschedule
                      </button>
                    </div>
                  </div>
                ))}

                {getSessionsForDate(selectedDate).length === 0 && (
                  <div className="text-center py-8">
                    <Calendar size={32} className="mx-auto text-gray-400 mb-2" />
                    <p className="text-gray-400 text-sm">No sessions scheduled</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorshipCalendarPage;

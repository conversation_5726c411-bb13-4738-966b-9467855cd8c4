import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  MessageSquare,
  Users,
  TrendingUp,
  Clock,
  Star,
  ArrowRight,
  Plus,
  Search,
  Filter,
  Pin,
  MessageCircle,
  Eye,
  ThumbsUp,
  AlertCircle
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';
import { apiClient } from '../../services/api';

const ForumsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // ✅ REAL DATA: Initialize with empty state and load from API
  const [forumCategories, setForumCategories] = useState([]);

  // ✅ REAL DATA: Initialize with empty state and load from API
  const [recentTopics, setRecentTopics] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch forum data from API
  useEffect(() => {
    const fetchForumData = async () => {
      try {
        setLoading(true);

        // ✅ REAL DATA: Fetch forum categories and topics from API
        const [categoriesResponse, topicsResponse] = await Promise.all([
          apiClient.get('/api/forums/categories/'),
          apiClient.get('/api/forums/topics/')
        ]);

        setForumCategories(categoriesResponse.data.results || []);
        setRecentTopics(topicsResponse.data.results || []);

      } catch (error) {
        console.error('Failed to fetch forum data:', error);

        // ✅ REAL DATA: Show error state instead of mock data
        setForumCategories([]);
        setRecentTopics([]);

        // TODO: Show user-friendly error message
        // toast.error(t('errors.failedToLoadForumData'));

      } finally {
        setLoading(false);
      }
    };

    fetchForumData();
  }, []);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-300">Please log in to access the forums.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white">Community Forums</h1>
            <p className="text-gray-300 mt-1">
              Connect, share, and learn from the entrepreneurial community
            </p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center">
              <Plus size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              New Topic
            </button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className={`flex flex-col sm:flex-row gap-4 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="relative flex-1">
            <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
            <input
              type="text"
              placeholder="Search topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full bg-white/10 border border-white/20 rounded-lg py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Categories</option>
            {forumCategories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Forum Categories */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold text-white mb-4">Forum Categories</h2>
            <div className="space-y-4">
              {forumCategories.map(category => (
                <div key={category.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-colors cursor-pointer">
                  <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-12 h-12 rounded-lg ${category.color} flex items-center justify-center text-2xl mr-4 ${isRTL ? "ml-4 mr-0" : ""}`}>
                        {category.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white">{category.name}</h3>
                        <p className="text-gray-400 text-sm">{category.description}</p>
                      </div>
                    </div>
                    <ArrowRight size={20} className="text-gray-400" />
                  </div>
                  
                  <div className={`flex justify-between items-center mt-4 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <span>{category.topicsCount} topics</span>
                      <span>{category.postsCount} posts</span>
                    </div>
                    <span>Last activity: {formatTimeAgo(category.lastActivity)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Topics Sidebar */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-semibold text-white mb-4">Recent Topics</h2>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
              <div className="p-4 border-b border-white/20">
                <h3 className="font-semibold text-white">Latest Discussions</h3>
              </div>
              <div className="divide-y divide-white/10">
                {recentTopics.map(topic => (
                  <div key={topic.id} className="p-4 hover:bg-white/5 transition-colors cursor-pointer">
                    <div className={`flex items-start justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        {topic.isPinned && <Pin size={14} className="text-yellow-400" />}
                        {topic.isHot && <TrendingUp size={14} className="text-red-400" />}
                      </div>
                    </div>
                    <h4 className="text-white font-medium text-sm mb-1 line-clamp-2">
                      {topic.title}
                    </h4>
                    <p className="text-gray-400 text-xs mb-2">
                      by {topic.author} in {topic.category}
                    </p>
                    <div className={`flex justify-between items-center text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className="flex items-center">
                          <MessageCircle size={12} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                          {topic.replies}
                        </span>
                        <span className="flex items-center">
                          <Eye size={12} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                          {topic.views}
                        </span>
                      </div>
                      <span>{formatTimeAgo(topic.lastReply)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Forum Stats */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 mt-6 p-4">
              <h3 className="font-semibold text-white mb-3">Forum Statistics</h3>
              <div className="space-y-2 text-sm">
                <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-400">Total Topics</span>
                  <span className="text-white">346</span>
                </div>
                <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-400">Total Posts</span>
                  <span className="text-white">2,422</span>
                </div>
                <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-400">Active Members</span>
                  <span className="text-white">1,234</span>
                </div>
                <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-400">Online Now</span>
                  <span className="text-green-400">89</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForumsPage;

/**
 * NAVIGATION CONFIGURATION
 * Centralized navigation configuration with role-based access control
 * NOW SYNCHRONIZED with centralizedRoleRouteMapping.ts
 */

import { UserRole } from '../utils/unifiedRoleManager';
import { ROUTE_ROLE_MAPPINGS, getRouteConfig } from './centralizedRoleRouteMapping';

export interface NavItem {
  id: string;
  name: string;
  path: string;
  icon: string; // ✅ FIXED: Use string icon names instead of JSX
  allowedRoles: UserRole[];
  category: 'main' | 'content' | 'system' | 'security' | 'super_admin' | 'ai';
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  children?: NavItem[];
}

/**
 * AUTHORITATIVE NAVIGATION CONFIGURATION
 * Single source of truth for all navigation items and their role requirements
 */
export const NAVIGATION_ITEMS: NavItem[] = [
  // Main Dashboard - All authenticated users
  {
    id: 'dashboard',
    name: 'dashboard.title',
    path: '/dashboard',
    icon: 'Home',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'main'
  },

  // Business & Ideas - FIXED: Allow advisors (mentors/investors) access
  {
    id: 'business-ideas',
    name: 'businessIdeas.title',
    path: '/dashboard/business-ideas',
    icon: 'Lightbulb',
    allowedRoles: ['user', 'mentor', 'investor'], // FIXED: Synchronized with route mapping
    category: 'main'
  },
  {
    id: 'business-plans',
    name: 'businessPlans.title',
    path: '/dashboard/business-plans',
    icon: 'FileText',
    allowedRoles: ['user', 'mentor', 'investor'], // FIXED: Synchronized with route mapping
    category: 'main'
  },
  {
    id: 'incubator',
    name: 'incubator.title',
    path: '/dashboard/incubator',
    icon: 'Building',
    allowedRoles: ['user', 'mentor', 'investor'], // FIXED: Synchronized with route mapping
    category: 'main'
  },

  // Content & Resources - Role-specific access
  {
    id: 'posts',
    name: 'posts.title',
    path: '/dashboard/posts',
    icon: 'MessageSquare',
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'content'
  },
  {
    id: 'events',
    name: 'events.title',
    path: '/dashboard/events',
    icon: 'Calendar',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin', 'moderator'],
    category: 'content'
  },
  {
    id: 'resources',
    name: 'resources.title',
    path: '/dashboard/resources',
    icon: 'BookOpen',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin', 'moderator'],
    category: 'content'
  },
  {
    id: 'templates',
    name: 'templates.title',
    path: '/dashboard/templates',
    icon: 'FileText',
    allowedRoles: ['user'],
    category: 'content'
  },
  {
    id: 'funding',
    name: 'funding.title',
    path: '/dashboard/funding',
    icon: 'DollarSign',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin'],
    category: 'content'
  },

  // Analytics & Insights - Role-specific analytics
  {
    id: 'analytics',
    name: 'analytics.title',
    path: '/dashboard/analytics',
    icon: 'BarChart3',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'moderator'],
    category: 'main'
  },

  // Mentor-specific features
  {
    id: 'mentorship-sessions',
    name: 'mentorship.sessions.title',
    path: '/dashboard/mentorship/sessions',
    icon: 'Users',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'mentees',
    name: 'mentorship.mentees.title',
    path: '/dashboard/mentorship/mentees',
    icon: 'UserCheck',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'mentorship-analytics',
    name: 'mentorship.analytics.title',
    path: '/dashboard/mentorship/analytics',
    icon: 'BarChart3',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'mentorship-calendar',
    name: 'mentorship.calendar.title',
    path: '/dashboard/mentorship/calendar',
    icon: 'Calendar',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'mentorship-resources',
    name: 'mentorship.resources.title',
    path: '/dashboard/mentorship/resources',
    icon: 'BookOpen',
    allowedRoles: ['mentor'],
    category: 'content'
  },

  // Investor-specific features
  {
    id: 'investment-opportunities',
    name: 'investments.opportunities.title',
    path: '/dashboard/investment/opportunities', // FIXED: Match route mapping
    icon: 'Star',
    allowedRoles: ['investor'],
    category: 'main'
  },
  {
    id: 'portfolio',
    name: 'investments.portfolio.title',
    path: '/dashboard/investment/portfolio',
    icon: 'PieChart',
    allowedRoles: ['investor'],
    category: 'main'
  },

  // Moderator-specific features
  {
    id: 'content-moderation',
    name: 'moderation.content.title',
    path: '/dashboard/moderation/content',
    icon: 'Shield',
    allowedRoles: ['moderator'],
    category: 'main'
  },
  {
    id: 'user-moderation',
    name: 'moderation.users.title',
    path: '/dashboard/moderation/users',
    icon: 'Users',
    allowedRoles: ['moderator'],
    category: 'main'
  },

  // Forums - Community communication for business-focused users
  {
    id: 'forums',
    name: 'forums.title',
    path: '/dashboard/forums', // FIXED: Match route mapping
    icon: 'MessageSquare',
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'content'
  },

  // Chat - Direct messaging and communication
  {
    id: 'chat',
    name: 'chat.title',
    path: '/chat',
    icon: 'MessageSquare',
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'content'
  },

  // AI Features - Available to business-focused users
  {
    id: 'ai-assistant',
    name: 'ai.assistant.title',
    path: '/dashboard/ai', // FIXED: Match route mapping
    icon: 'Bot',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'], // FIXED: Match route mapping
    category: 'ai'
  },

  // Admin Features - Only for admins and super_admins
  {
    id: 'user-management',
    name: 'admin.users.title',
    path: '/admin/users',
    icon: 'Users',
    allowedRoles: ['admin', 'super_admin'],
    category: 'system'
  },
  {
    id: 'admin-analytics',
    name: 'admin.analytics.title',
    path: '/admin/analytics',
    icon: 'BarChart3',
    allowedRoles: ['admin', 'super_admin'],
    category: 'system'
  },
  {
    id: 'system-settings',
    name: 'admin.settings.title',
    path: '/admin/settings',
    icon: 'Settings',
    allowedRoles: ['admin', 'super_admin'],
    category: 'system'
  },

  // Super Admin Features - CRITICAL ACCESS
  {
    id: 'super-admin-dashboard',
    name: 'superAdmin.dashboard.title',
    path: '/super_admin',
    icon: 'Shield',
    allowedRoles: ['super_admin'],
    category: 'super_admin',
    riskLevel: 'critical'
  },
  {
    id: 'system-monitoring',
    name: 'superAdmin.monitoring.title',
    path: '/super_admin/monitoring',
    icon: 'Monitor',
    allowedRoles: ['super_admin'],
    category: 'super_admin',
    riskLevel: 'critical'
  },
  {
    id: 'ai-system-management',
    name: 'superAdmin.ai.title',
    path: '/super_admin/ai-system-management',
    icon: 'Bot',
    allowedRoles: ['super_admin'],
    category: 'system',
    riskLevel: 'high'
  },

  // User Profile & Settings - Available to all roles
  {
    id: 'profile',
    name: 'profile.title',
    path: '/profile',
    icon: 'User',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'main'
  },
  {
    id: 'settings',
    name: 'settings.title',
    path: '/settings',
    icon: 'Settings',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'main'
  }
];

/**
 * Get navigation items filtered by user role
 */
export function getNavigationItemsForRole(userRole: UserRole): NavItem[] {
  return NAVIGATION_ITEMS.filter(item => item.allowedRoles.includes(userRole));
}

// ✅ REMOVED: Duplicate function - use getNavigationItemsForRole() directly


/**
 * Check if a user role can access a specific navigation item
 */
export function canAccessNavItem(userRole: UserRole, itemId: string): boolean {
  const item = NAVIGATION_ITEMS.find(item => item.id === itemId);
  return item ? item.allowedRoles.includes(userRole) : false;
}

import React, { useState, useEffect } from 'react';
import { Zap, Clock, Database, Server, Globe, TrendingUp, AlertTriangle, CheckCircle, Cpu, Memory } from 'lucide-react';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  target: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  trend: number;
  icon: React.ReactNode;
  color: string;
  history: number[];
}

interface OptimizationSuggestion {
  id: string;
  category: 'database' | 'frontend' | 'backend' | 'infrastructure';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  implemented: boolean;
}

const PerformanceCenter: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'optimization'>('overview');

  // ✅ REAL DATA: Fetch performance metrics from API
  useEffect(() => {
    const fetchPerformanceMetrics = async () => {
      try {
        setLoading(true);
        // Fetch real performance data from backend
        const response = await fetch('/api/admin/performance/metrics/');
        const data = await response.json();

        if (response.ok) {
          setMetrics(data.metrics || []);
        } else {
          throw new Error(data.message || 'Failed to fetch performance metrics');
        }
      } catch (error) {
        console.error('Error fetching performance metrics:', error);
        // Set empty array on error - no mock data fallback
        setMetrics([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPerformanceMetrics();

    // Also fetch optimization suggestions
    const fetchSuggestions = async () => {
      try {
        const suggestionsResponse = await fetch('/api/admin/performance/suggestions/');
        const suggestionsData = await suggestionsResponse.json();

        if (suggestionsResponse.ok) {
          setSuggestions(suggestionsData.suggestions || []);
        } else {
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
        setSuggestions([]);
      }
    };

    fetchPerformanceMetrics();
    fetchSuggestions();
  }, []);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500/20 text-red-300';
      case 'high':
        return 'bg-orange-500/20 text-orange-300';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-300';
      case 'low':
        return 'bg-green-500/20 text-green-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'database':
        return <Database size={16} className="text-purple-400" />;
      case 'frontend':
        return <Globe size={16} className="text-blue-400" />;
      case 'backend':
        return <Server size={16} className="text-green-400" />;
      case 'infrastructure':
        return <Zap size={16} className="text-yellow-400" />;
      default:
        return <AlertTriangle size={16} className="text-gray-400" />;
    }
  };

  // Tabs
  const tabs = [
    { id: 'overview', label: t('performance.overview', 'Overview'), icon: <TrendingUp size={16} /> },
    { id: 'optimization', label: t('performance.optimization', 'Optimization'), icon: <Zap size={16} /> }
  ];

  return (
    <DashboardLayout currentPage="performance">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.performance.center', 'Performance Center')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.performance.description', 'Monitor and optimize platform performance')}</div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className={`flex space-x-1 bg-indigo-900/30 p-1 rounded-lg ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-indigo-800/50'
              } ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div>
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {metrics.map((metric) => (
              <div key={metric.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={metric.color}>
                    {metric.icon}
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    metric.status === 'excellent' ? 'bg-green-500/20 text-green-300' :
                    metric.status === 'good' ? 'bg-blue-500/20 text-blue-300' :
                    metric.status === 'warning' ? 'bg-yellow-500/20 text-yellow-300' :
                    'bg-red-500/20 text-red-300'
                  }`}>
                    {metric.status}
                  </span>
                </div>
                
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-white mb-1">
                    {metric.value}{metric.unit}
                  </h3>
                  <p className="text-gray-400 text-sm">{metric.name}</p>
                  <div className={`flex items-center space-x-1 text-xs mt-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <span className="text-gray-400">{t('performance.target', 'Target')}: {metric.target}{metric.unit}</span>
                  </div>
                </div>
                
                {/* Mini Chart */}
                <div className="h-8 flex items-end space-x-1">
                  {metric.history.map((value, index) => (
                    <div
                      key={index}
                      className={`flex-1 rounded-t ${
                        value <= metric.target * 0.7 ? 'bg-green-500' :
                        value <= metric.target ? 'bg-blue-500' : 'bg-red-500'
                      } opacity-70`}
                      style={{ height: `${(value / (metric.target * 1.2)) * 100}%` }}
                    ></div>
                  ))}
                </div>
                
                <div className={`flex items-center space-x-1 text-xs mt-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <TrendingUp size={12} className={metric.trend >= 0 ? 'text-red-400' : 'text-green-400'} />
                  <span className={metric.trend >= 0 ? 'text-red-400' : 'text-green-400'}>
                    {metric.trend > 0 ? '+' : ''}{metric.trend.toFixed(1)}%
                  </span>
                  <span className="text-gray-400">{t('performance.vs.last.week', 'vs last week')}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'optimization' && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className="text-lg font-semibold text-white mb-6">{t('performance.optimization.suggestions', 'Optimization Suggestions')}</h3>
          
          <div className="space-y-4">
            {suggestions.map((suggestion) => (
              <div key={suggestion.id} className={`p-4 bg-indigo-800/30 rounded-lg border-l-4 ${
                suggestion.priority === 'critical' ? 'border-l-red-500' :
                suggestion.priority === 'high' ? 'border-l-orange-500' :
                suggestion.priority === 'medium' ? 'border-l-yellow-500' : 'border-l-green-500'
              }`}>
                <div className={`flex items-start justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    {getCategoryIcon(suggestion.category)}
                    <div>
                      <h4 className="text-white font-medium">{suggestion.title}</h4>
                      <p className="text-gray-400 text-sm capitalize">{suggestion.category}</p>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(suggestion.priority)}`}>
                      {suggestion.priority}
                    </span>
                    {suggestion.implemented && (
                      <CheckCircle size={16} className="text-green-400" />
                    )}
                  </div>
                </div>
                
                <p className="text-gray-300 text-sm mb-3">{suggestion.description}</p>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">{t('performance.impact', 'Impact')}: </span>
                    <span className="text-green-400">{suggestion.impact}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('performance.effort', 'Effort')}: </span>
                    <span className={
                      suggestion.effort === 'low' ? 'text-green-400' :
                      suggestion.effort === 'medium' ? 'text-yellow-400' : 'text-red-400'
                    }>
                      {suggestion.effort}
                    </span>
                  </div>
                </div>
                
                {!suggestion.implemented && (
                  <div className="mt-3">
                    <button className="px-3 py-1 bg-purple-600/80 hover:bg-purple-500/80 rounded text-white text-sm transition-colors">
                      {t('performance.implement', 'Implement')}
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default PerformanceCenter;

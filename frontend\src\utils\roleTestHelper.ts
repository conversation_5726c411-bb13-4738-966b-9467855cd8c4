/**
 * ROLE TEST HELPER
 * Browser console helper to test role detection and navigation
 */

import { getUserRole } from './unifiedRoleManager';
import { getNavigationItemsForRole } from '../config/navigationConfig';

/**
 * Test role detection with different user objects
 */
export function testRoleDetection() {
  console.log('🧪 TESTING ROLE DETECTION');
  console.log('=' .repeat(40));

  const testUsers = [
    {
      name: 'Regular User',
      user: {
        id: 1,
        username: 'user1',
        user_role: 'user',
        is_staff: false,
        is_superuser: false,
        profile: null
      },
      expected: 'user'
    },
    {
      name: 'Mentor User',
      user: {
        id: 2,
        username: 'mentor1',
        user_role: 'mentor',
        is_staff: false,
        is_superuser: false,
        profile: {
          primary_role: { name: 'mentor' }
        }
      },
      expected: 'mentor'
    },
    {
      name: 'Investor User',
      user: {
        id: 3,
        username: 'investor1',
        user_role: 'investor',
        is_staff: false,
        is_superuser: false,
        profile: {
          primary_role: { name: 'investor' }
        }
      },
      expected: 'investor'
    },
    {
      name: 'Admin User',
      user: {
        id: 4,
        username: 'admin1',
        user_role: 'admin',
        is_staff: true,
        is_superuser: false,
        profile: null
      },
      expected: 'admin'
    }
  ];

  let passed = 0;
  let total = testUsers.length;

  testUsers.forEach(test => {
    const detected = getUserRole(test.user as any);
    const success = detected === test.expected;
    
    console.log(`${success ? '✅' : '❌'} ${test.name}: Expected "${test.expected}", Got "${detected}"`);
    
    if (success) {
      passed++;
    }
  });

  console.log(`\n📊 Results: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
  return { passed, total, success: passed === total };
}

/**
 * Test navigation items for different roles
 */
export function testNavigationItems() {
  console.log('\n🧪 TESTING NAVIGATION ITEMS');
  console.log('=' .repeat(40));

  const roles = ['user', 'mentor', 'investor', 'admin'] as const;
  
  roles.forEach(role => {
    const navItems = getNavigationItemsForRole(role);
    console.log(`📋 ${role.toUpperCase()}: ${navItems.length} navigation items`);
    
    // Show first few items
    navItems.slice(0, 3).forEach(item => {
      console.log(`   - ${item.id}: ${item.path}`);
    });
    
    if (navItems.length > 3) {
      console.log(`   ... and ${navItems.length - 3} more`);
    }
    console.log('');
  });
}

/**
 * Debug current user's role and navigation
 */
export function debugCurrentUser() {
  console.log('\n🔍 DEBUGGING CURRENT USER');
  console.log('=' .repeat(40));

  // Try to get current user from Redux store
  const store = (window as any).store;
  if (!store) {
    console.log('❌ Redux store not available');
    return;
  }

  const state = store.getState();
  const user = state?.auth?.user;
  
  if (!user) {
    console.log('❌ No user found in auth state');
    return;
  }

  console.log('👤 Current User:');
  console.log('   ID:', user.id);
  console.log('   Username:', user.username);
  console.log('   user_role:', user.user_role);
  console.log('   is_staff:', user.is_staff);
  console.log('   is_superuser:', user.is_superuser);
  console.log('   profile:', user.profile);

  const detectedRole = getUserRole(user);
  console.log('\n🎯 Detected Role:', detectedRole);

  const navItems = getNavigationItemsForRole(detectedRole);
  console.log('\n📋 Navigation Items:', navItems.length);
  navItems.forEach(item => {
    console.log(`   - ${item.id}: ${item.path}`);
  });

  return {
    user,
    detectedRole,
    navigationCount: navItems.length,
    navigationItems: navItems.map(item => ({ id: item.id, path: item.path }))
  };
}

/**
 * Test role switching simulation
 */
export function simulateRoleSwitch(targetRole: string) {
  console.log(`\n🔄 SIMULATING ROLE SWITCH TO: ${targetRole.toUpperCase()}`);
  console.log('=' .repeat(40));

  const mockUser = {
    id: 999,
    username: `test_${targetRole}`,
    user_role: targetRole,
    is_staff: targetRole === 'admin' || targetRole === 'super_admin',
    is_superuser: targetRole === 'super_admin',
    profile: targetRole !== 'user' ? {
      primary_role: { name: targetRole }
    } : null
  };

  const detectedRole = getUserRole(mockUser as any);
  console.log('🎯 Detected Role:', detectedRole);

  const navItems = getNavigationItemsForRole(detectedRole);
  console.log('📋 Navigation Items:', navItems.length);
  
  console.log('\n📋 Navigation Preview:');
  navItems.forEach(item => {
    console.log(`   - ${item.id}: ${item.path}`);
  });

  return {
    mockUser,
    detectedRole,
    navigationItems: navItems
  };
}

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).roleTest = {
    testDetection: testRoleDetection,
    testNavigation: testNavigationItems,
    debugCurrent: debugCurrentUser,
    simulateRole: simulateRoleSwitch
  };

  console.log('🎯 Role Test Helper loaded!');
  console.log('Available commands:');
  console.log('  window.roleTest.testDetection()');
  console.log('  window.roleTest.testNavigation()');
  console.log('  window.roleTest.debugCurrent()');
  console.log('  window.roleTest.simulateRole("mentor")');
}

export default {
  testRoleDetection,
  testNavigationItems,
  debugCurrentUser,
  simulateRoleSwitch
};

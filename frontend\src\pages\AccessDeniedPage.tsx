import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Shield, ArrowLeft, Home, LogIn, AlertTriangle } from 'lucide-react';
import { useAppSelector } from '../store/hooks';
import { isSuperAdmin, isAdmin, getUserRole } from '../utils/unifiedRoleManager';
import { useLanguage } from '../hooks/useLanguage';
import { RTLText, RTLFlex } from '../components/common';

/**
 * Access Denied page component
 * Shows when users don't have permission to access a specific route
 */
const AccessDeniedPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Get error message from session storage or URL params
  useEffect(() => {
    const storedError = sessionStorage.getItem('accessError');
    const urlParams = new URLSearchParams(location.search);
    const urlError = urlParams.get('error');
    
    if (storedError) {
      setErrorMessage(storedError);
      sessionStorage.removeItem('accessError'); // Clear after reading
    } else if (urlError) {
      setErrorMessage(decodeURIComponent(urlError));
    } else {
      setErrorMessage(t('access.general.denied', 'You don\'t have permission to access this page.'));
    }
  }, [location.search, t]);

  const getDashboardPath = () => {
    if (!user) return '/';

    // ✅ UNIFIED: Use unified role manager for dashboard routing
    const userRole = getUserRole(user);
    return `/dashboard/${userRole}`;
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate(getDashboardPath());
    }
  };

  const handleGoHome = () => {
    navigate(getDashboardPath());
  };

  const getUserRoleInfo = () => {
    if (!user) return null;

    const roles = user.profile?.active_roles?.map(role => role.name) || [];
    const primaryRole = user.profile?.primary_role?.name;

    return {
      roles,
      primaryRole,
      isAdmin: isAdmin(user), // FIXED: Use unified role manager
      isSuperAdmin: isSuperAdmin(user)
    };
  };

  const roleInfo = getUserRoleInfo();

  return (
    <div className={`min-h-screen bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 flex items-center justify-center px-4 ${isRTL ? 'font-arabic' : ''}`}>
      <div className="max-w-2xl w-full text-center">
        {/* Access Denied Header */}
        <div className="mb-12">
          <div className="relative mb-8">
            <div className="mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <Shield className="w-12 h-12 text-red-600 dark:text-red-400" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            <RTLText text={t('access.denied.title', 'Access Denied')} />
          </h1>
          
          <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 max-w-md mx-auto">
            <RTLText text={errorMessage} />
          </p>

          {/* User Role Information */}
          {isAuthenticated && roleInfo && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mb-8 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <RTLText text={t('access.yourPermissions', 'Your Current Permissions')} />
              </h3>
              
              <div className={`grid gap-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div>
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    <RTLText text={t('user.primaryRole', 'Primary Role:')} />
                  </span>
                  <span className="ml-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-sm">
                    <RTLText text={roleInfo.primaryRole || 'user'} />
                  </span>
                </div>
                
                {roleInfo.roles.length > 0 && (
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      <RTLText text={t('user.activeRoles', 'Active Roles:')} />
                    </span>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {roleInfo.roles.map((role, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded text-sm"
                        >
                          <RTLText text={role} />
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <RTLFlex className="flex-wrap gap-4 justify-center mb-8">
          <button
            onClick={handleGoBack}
            className={`inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <ArrowLeft className={`w-5 h-5 ${isRTL ? 'ml-2 rotate-180' : 'mr-2'}`} />
            <RTLText text={t('common.goBack', 'Go Back')} />
          </button>
          
          {isAuthenticated ? (
            <button
              onClick={handleGoHome}
              className={`inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <Home className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <RTLText text={t('navigation.dashboard', 'Go to Dashboard')} />
            </button>
          ) : (
            <button
              onClick={() => navigate('/login')}
              className={`inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <LogIn className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <RTLText text={t('auth.login', 'Login')} />
            </button>
          )}
        </RTLFlex>

        {/* Help Information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-3">
            <RTLText text={t('access.needAccess', 'Need Access?')} />
          </h3>
          <p className="text-blue-800 dark:text-blue-300 text-sm mb-4">
            <RTLText text={t('access.contactAdmin', 'If you believe you should have access to this page, please contact your administrator or check if you need to:')} />
          </p>
          <ul className={`text-blue-800 dark:text-blue-300 text-sm space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
            <li>• <RTLText text={t('access.suggestion.login', 'Log in with the correct account')} /></li>
            <li>• <RTLText text={t('access.suggestion.role', 'Request the appropriate role or permissions')} /></li>
            <li>• <RTLText text={t('access.suggestion.contact', 'Contact support for assistance')} /></li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AccessDeniedPage;

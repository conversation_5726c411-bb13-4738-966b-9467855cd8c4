/**
 * Unified Dashboard Component
 * Consolidated dashboard that adapts to different user roles
 * Eliminates code duplication across dashboard implementations
 */

import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { ErrorDisplay, useSessionError } from '../../ui';
import {
  selectDashboard,
  selectDashboardRole,
  selectDashboardConfig,
  selectDashboardLoading,
  selectDashboardErrors,
  setRole,
  fetchDashboardStats,
  DashboardRole
} from '../../../store/dashboardSlice';
import { UnifiedDashboardProps } from '../../../types/dashboard';
import { getGridLayout } from '../../../utils/dashboardUtils';
import { getUserRole } from '../../../utils/unifiedRoleManager';

// Import unified section components
import UnifiedWelcomeSection from './sections/UnifiedWelcomeSection';
import UnifiedStatsSection from './sections/UnifiedStatsSection';
import UnifiedQuickActionsSection from './sections/UnifiedQuickActionsSection';
import UnifiedAnalyticsSection from './sections/UnifiedAnalyticsSection';
import UnifiedActivitySection from './sections/UnifiedActivitySection';
import UnifiedNotificationsSection from './sections/UnifiedNotificationsSection';
import UnifiedSystemHealthSection from './sections/UnifiedSystemHealthSection';

/**
 * Dashboard Content Component (inside provider)
 */
const DashboardContent: React.FC<{
  className?: string;
  onSectionUpdate?: (sectionId: string, data: any) => void;
}> = ({ className, onSectionUpdate }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { error: accessError, clearError: clearAccessError } = useSessionError('accessError');

  const role = useAppSelector(selectDashboardRole);
  const config = useAppSelector(selectDashboardConfig);
  const loading = useAppSelector(selectDashboardLoading);
  const errors = useAppSelector(selectDashboardErrors);
  const gridLayout = getGridLayout(role);

  // Initialize dashboard data
  useEffect(() => {
    if (user) {
      const userRole = getUserRole(user);
      dispatch(setRole(userRole));
      dispatch(fetchDashboardStats(userRole));
    }
  }, [dispatch, user]);

  // Handle section updates
  const handleSectionUpdate = (sectionId: string, data: any) => {
    onSectionUpdate?.(sectionId, data);
  };

  // Render section based on type and role
  const renderSection = (sectionType: string, order: number) => {
    const sectionProps = {
      role,
      onUpdate: handleSectionUpdate,
      className: "mb-8",
    };

    switch (sectionType) {
      case 'welcome':
        return (
          <UnifiedWelcomeSection
            key={`welcome-${order}`}
            user={user}
            {...sectionProps}
          />
        );
      
      case 'stats':
        return (
          <UnifiedStatsSection
            key={`stats-${order}`}
            {...sectionProps}
          />
        );
      
      case 'quick_actions':
        return (
          <UnifiedQuickActionsSection
            key={`quick_actions-${order}`}
            {...sectionProps}
          />
        );
      
      case 'analytics':
        return (
          <UnifiedAnalyticsSection
            key={`analytics-${order}`}
            {...sectionProps}
          />
        );
      
      case 'activity':
        return (
          <UnifiedActivitySection
            key={`activity-${order}`}
            {...sectionProps}
          />
        );
      
      case 'notifications':
        return (
          <UnifiedNotificationsSection
            key={`notifications-${order}`}
            {...sectionProps}
          />
        );
      
      case 'system_health':
        return (
          <UnifiedSystemHealthSection
            key={`system_health-${order}`}
            {...sectionProps}
          />
        );
      
      default:
        return null;
    }
  };

  // Get sections to render based on role
  const getSectionsToRender = () => {
    const roleSections: Record<DashboardRole, string[]> = {
      user: ['welcome', 'stats', 'quick_actions'],
      mentor: ['welcome', 'stats', 'quick_actions', 'activity'],
      investor: ['welcome', 'stats', 'quick_actions', 'analytics'],
      moderator: ['welcome', 'stats', 'quick_actions', 'activity', 'notifications'],
      admin: ['welcome', 'stats', 'quick_actions', 'analytics', 'activity', 'notifications'],
      super_admin: ['welcome', 'stats', 'quick_actions', 'analytics', 'activity', 'system_health', 'notifications'],
    };

    return roleSections[role] || roleSections.user;
  };

  if (loading.global) {
    return (
      <div className={`min-h-screen ${getBackgroundClasses()} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <p className={`${getTextClasses('primary')} text-lg`}>
            {t('dashboard.loading', 'Loading dashboard...')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`min-h-screen ${getBackgroundClasses()} ${isRTL ? 'rtl' : 'ltr'} ${className || ''}`}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      <div className={`px-4 sm:px-6 lg:px-8 py-6 ${isRTL ? 'text-right' : 'text-left'}`}>
        <div className={`${gridLayout.maxWidth} mx-auto`}>
          
          {/* Access Error Display */}
          {accessError && (
            <ErrorDisplay
              error={accessError}
              type="warning"
              onDismiss={clearAccessError}
              className="mb-6"
            />
          )}

          {/* Dashboard Errors */}
          {errors.map((error, index) => (
            <ErrorDisplay
              key={index}
              error={error.message}
              type={error.type}
              onDismiss={() => clearError?.(index.toString())}
              className="mb-6"
            />
          ))}

          {/* Dashboard Sections */}
          <div className="space-y-8">
            {getSectionsToRender().map((sectionType, index) => 
              renderSection(sectionType, index)
            )}
          </div>

          {/* Debug Info (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8 p-4 bg-black/20 rounded-lg border border-gray-500">
              <h3 className={`${getTextClasses('primary')} font-semibold mb-2`}>
                Debug Info
              </h3>
              <div className={`${getTextClasses('secondary')} text-sm space-y-1`}>
                <p>Role: {role}</p>
                <p>Theme: {theme.role}</p>
                <p>Sections: {getSectionsToRender().join(', ')}</p>
                <p>Loading: {JSON.stringify(loading)}</p>
                <p>Errors: {errors.length}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Main Unified Dashboard Component (with provider wrapper)
 */
const UnifiedDashboard: React.FC<UnifiedDashboardProps> = ({
  role: providedRole,
  user: providedUser,
  config,
  className,
  onSectionUpdate,
}) => {
  const dispatch = useAppDispatch();
  const { user: storeUser } = useAppSelector(state => state.auth);
  const currentUser = providedUser || storeUser;
  const dashboardRole = providedRole || getUserRole(currentUser);

  // Set the dashboard role and config when component mounts
  useEffect(() => {
    dispatch(setRole(dashboardRole));
    if (config) {
      // dispatch(updateConfig(config)); // Uncomment when needed
    }
  }, [dispatch, dashboardRole, config]);

  return (
    <DashboardContent
      className={className}
      onSectionUpdate={onSectionUpdate}
    />
  );
};

export default UnifiedDashboard;

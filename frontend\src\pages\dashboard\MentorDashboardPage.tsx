import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Users,
  Calendar,
  Clock,
  Video,
  MessageSquare,
  Star,
  ArrowRight,
  Plus,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  TrendingUp,
  Award,
  BookOpen
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';
import { apiClient } from '../../services/api';

const MentorDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not a mentor
  useEffect(() => {
    if (isAuthenticated && userRole !== 'mentor') {
      console.warn('Access denied: User is not a mentor');
      navigate('/dashboard');
    }
  }, [isAuthenticated, userRole, navigate]);

  // ✅ REAL DATA: Mentor dashboard data from API
  const [mentorStats, setMentorStats] = useState(null);
  const [upcomingSessions, setUpcomingSessions] = useState([]);
  const [recentMentees, setRecentMentees] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch mentor dashboard data from API
  useEffect(() => {
    const fetchMentorData = async () => {
      try {
        setLoading(true);

        // ✅ REAL DATA: Fetch mentor stats from API
        const [statsResponse, sessionsResponse, menteesResponse] = await Promise.all([
          apiClient.get('/api/roles/mentor/dashboard-stats/'),
          apiClient.get('/api/roles/mentor/upcoming-sessions/'),
          apiClient.get('/api/roles/mentor/my-mentees/')
        ]);

        setMentorStats(statsResponse.data);
        setUpcomingSessions(sessionsResponse.data.results || []);
        setRecentMentees(menteesResponse.data.results || []);

      } catch (error) {
        console.error('Failed to fetch mentor dashboard data:', error);

        // ✅ REAL DATA: Show error state instead of mock data
        setMentorStats(null);
        setUpcomingSessions([]);
        setRecentMentees([]);

        // TODO: Show user-friendly error message
        // toast.error(t('errors.failedToLoadDashboard'));

      } finally {
        setLoading(false);
      }
    };

    fetchMentorData();
  }, []);

  const formatSessionTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  // Don't render if not authenticated or not a mentor
  if (!isAuthenticated || userRole !== 'mentor') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-300">This page is only accessible to mentors.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white">Mentor Dashboard</h1>
            <p className="text-gray-300 mt-1">
              Welcome back, {user?.first_name}! Manage your mentees and sessions.
            </p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <Link
              to="/dashboard/mentorship/availability"
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center"
            >
              <Calendar size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Set Availability
            </Link>
            <Link
              to="/dashboard/mentorship/sessions/schedule"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center"
            >
              <Plus size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Schedule Session
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Mentees</p>
                <p className="text-2xl font-bold text-white">{mentorStats.activeMentees}</p>
              </div>
              <Users className="text-blue-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Sessions</p>
                <p className="text-2xl font-bold text-white">{mentorStats.totalSessions}</p>
              </div>
              <Video className="text-green-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Upcoming</p>
                <p className="text-2xl font-bold text-white">{mentorStats.upcomingSessions}</p>
              </div>
              <Clock className="text-yellow-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Avg Rating</p>
                <p className="text-2xl font-bold text-white">{mentorStats.averageRating}</p>
              </div>
              <Star className="text-yellow-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Hours</p>
                <p className="text-2xl font-bold text-white">{mentorStats.totalHours}</p>
              </div>
              <TrendingUp className="text-purple-400" size={24} />
            </div>
          </div>
        </div>

        {/* Upcoming Sessions */}
        <div className="mb-8">
          <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <Calendar size={20} className={`mr-2 text-purple-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Upcoming Sessions
            </h2>
            <Link
              to="/dashboard/mentorship/sessions"
              className="text-purple-400 hover:text-purple-300 flex items-center text-sm"
            >
              View All <ArrowRight size={16} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {upcomingSessions.map(session => (
              <div key={session.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className={`flex items-center mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center mr-3">
                    <Users size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white">{session.mentee.name}</h3>
                    <p className="text-gray-400 text-sm">{session.topic}</p>
                  </div>
                </div>
                
                <div className="text-gray-300 text-sm mb-3">
                  <div className="flex items-center mb-1">
                    <Calendar size={14} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                    {formatSessionTime(session.scheduledAt)}
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                    {session.duration} minutes
                  </div>
                </div>

                <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-xs bg-green-600/20 text-green-400 px-2 py-1 rounded">
                    {session.type === 'video_call' ? 'Video Call' : 'In Person'}
                  </span>
                  <Link
                    to={`/dashboard/mentorship/sessions/${session.id}`}
                    className="text-purple-400 hover:text-purple-300 text-sm flex items-center"
                  >
                    Join <ArrowRight size={14} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* My Mentees */}
        <div className="mb-8">
          <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <Users size={20} className={`mr-2 text-blue-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
              My Mentees
            </h2>
            <Link
              to="/dashboard/mentorship/mentees"
              className="text-blue-400 hover:text-blue-300 flex items-center text-sm"
            >
              Manage All <ArrowRight size={16} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {recentMentees.map(mentee => (
              <div key={mentee.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className={`flex items-center mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="w-12 h-12 rounded-full bg-indigo-600 flex items-center justify-center mr-3">
                    <Users size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white">{mentee.name}</h3>
                    <p className="text-gray-400 text-sm">{mentee.businessIdea}</p>
                  </div>
                </div>

                <div className="mb-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">Progress</span>
                    <span className="text-white">{mentee.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getProgressColor(mentee.progress)}`}
                      style={{ width: `${mentee.progress}%` }}
                    ></div>
                  </div>
                </div>

                <div className="text-gray-400 text-xs mb-3">
                  Last session: {new Date(mentee.lastSession).toLocaleDateString()}
                </div>

                <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`text-xs px-2 py-1 rounded ${
                    mentee.status === 'active' ? 'bg-green-600/20 text-green-400' : 'bg-gray-600/20 text-gray-400'
                  }`}>
                    {mentee.status}
                  </span>
                  <Link
                    to={`/dashboard/mentorship/mentees/${mentee.id}`}
                    className="text-purple-400 hover:text-purple-300 text-sm flex items-center"
                  >
                    View <ArrowRight size={14} className={`ml-1 ${isRTL ? "mr-1 ml-0" : ""}`} />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/dashboard/mentorship/analytics"
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center mb-3">
              <TrendingUp className="text-purple-400 mr-3" size={24} />
              <h3 className="text-lg font-medium text-white">View Analytics</h3>
            </div>
            <p className="text-gray-400 text-sm">Track your mentoring performance and impact</p>
          </Link>

          <Link
            to="/dashboard/mentorship/profile"
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center mb-3">
              <Award className="text-yellow-400 mr-3" size={24} />
              <h3 className="text-lg font-medium text-white">Mentor Profile</h3>
            </div>
            <p className="text-gray-400 text-sm">Update your expertise and availability</p>
          </Link>

          <Link
            to="/dashboard/resources"
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center mb-3">
              <BookOpen className="text-green-400 mr-3" size={24} />
              <h3 className="text-lg font-medium text-white">Resources</h3>
            </div>
            <p className="text-gray-400 text-sm">Access mentoring guides and tools</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MentorDashboardPage;

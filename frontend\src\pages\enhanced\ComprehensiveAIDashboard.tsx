/**
 * Comprehensive AI Dashboard - Unified management interface for all AI features
 * Combines monitoring, analytics, tutorials, and management in one place
 */

import React, { useState, useEffect } from 'react';
import {
  Brain,
  BarChart3,
  Settings,
  BookOpen,
  Monitor,
  Zap,
  Users,
  TrendingUp,
  Shield,
  Globe,
  Smartphone,
  Bell,
  Star,
  Activity,
  HelpCircle,
  ChevronRight,
  Plus,
  RefreshCw
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
// MainLayout removed - handled by routing system
import { useLanguage } from '../../hooks/useLanguage';
import { apiClient } from '../../services/api';

// Import available components
import { useCentralizedAI } from '../../hooks/useCentralizedAI';

interface DashboardTab {
  id: string;
  label: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  description: string;
}

export const ComprehensiveAIDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeTab, setActiveTab] = useState('overview');
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showTutorials, setShowTutorials] = useState(false);
  const [showMobileInterface, setShowMobileInterface] = useState(false);
  const [isFirstVisit, setIsFirstVisit] = useState(false);
  const [realTimeStats, setRealTimeStats] = useState<{
    status: { ai_workers_running: number };
    impact: { users_helped: number };
    isOnline: boolean;
    lastUpdate: Date;
  } | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Get AI data from centralized hook
  const { status, isStatusLoading, statusError, isAvailable } = useCentralizedAI();

  // Mock data for compatibility - TODO: Replace with real API data
  const stats = {
    ideas_enhanced: 12,
    opportunities_found: 8,
    total_actions_today: 25
  };
  const recentActions: any[] = [];
  const isAIRunning = isAvailable || status?.available || false;
  const performanceStatus = { status: 'Good' };
  const aiLoading = isStatusLoading;

  // Check authentication status
  useEffect(() => {
    setIsAuthenticated(true); // Simplified for now
  }, []);

  // Check if this is the user's first visit
  useEffect(() => {
    const hasVisited = localStorage.getItem('ai-dashboard-visited');
    if (!hasVisited) {
      setIsFirstVisit(true);
      setShowOnboarding(true);
      localStorage.setItem('ai-dashboard-visited', 'true');
    }
  }, []);

  // ✅ REAL DATA: Fetch real-time AI statistics from API
  useEffect(() => {
    const fetchRealTimeStats = async () => {
      try {
        // ✅ REAL DATA: Fetch real AI statistics from API
        const response = await apiClient.get('/api/ai/real-time-stats/');
        const statsData = response.data;

        setRealTimeStats({
          status: statsData.status || { ai_workers_running: 0 },
          impact: statsData.impact || { users_helped: 0 },
          isOnline: statsData.isOnline || false,
          lastUpdate: new Date(statsData.lastUpdate || Date.now())
        });

      } catch (error) {
        console.error('Failed to fetch real-time AI stats:', error);

        // ✅ REAL DATA: Show error state instead of mock data
        setRealTimeStats({
          status: { ai_workers_running: 0 },
          impact: { users_helped: 0 },
          isOnline: false,
          lastUpdate: new Date()
        });
      }
    };

    fetchRealTimeStats();

    // Refresh every 30 seconds
    const interval = setInterval(fetchRealTimeStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
    localStorage.setItem('ai-onboarding-completed', 'true');
  };

  const tabs: DashboardTab[] = [
    {
      id: 'overview',
      label: t('ai.tabs.overview', 'AI Overview'),
      icon: <Brain className="w-5 h-5" />,
      description: t('ai.tabs.overview.description', 'Unified access to all AI features'),
      component: (
        <div className="text-center py-8">
          <Brain className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {t('ai.overview.title', 'AI Overview Dashboard')}
          </h3>
          <p className="text-gray-600">
            {t('ai.overview.description', 'Comprehensive AI management interface coming soon')}
          </p>
        </div>
      )
    },
    {
      id: 'insights',
      label: t('ai.tabs.insights', 'AI Insights'),
      icon: <TrendingUp className="w-5 h-5" />,
      description: t('ai.tabs.insights.description', 'Personalized AI-generated insights'),
      component: (
        <div className="text-center py-8">
          <TrendingUp className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {t('ai.insights.title', 'AI Insights')}
          </h3>
          <p className="text-gray-600">
            {t('ai.insights.description', 'Personalized insights and recommendations')}
          </p>
        </div>
      )
    },
    {
      id: 'analytics',
      label: t('ai.tabs.performance', 'Performance'),
      icon: <BarChart3 className="w-5 h-5" />,
      description: t('ai.tabs.performance.description', 'AI performance and usage analytics'),
      component: (
        <div className="text-center py-8">
          <BarChart3 className="w-16 h-16 text-purple-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {t('ai.performance.title', 'Performance Analytics')}
          </h3>
          <p className="text-gray-600">
            {t('ai.performance.description', 'Detailed AI performance metrics and analytics')}
          </p>
        </div>
      )
    },
    {
      id: 'monitoring',
      label: t('ai.tabs.monitoring', 'System Health'),
      icon: <Monitor className="w-5 h-5" />,
      description: t('ai.tabs.monitoring.description', 'Real-time AI system monitoring'),
      component: (
        <div className="text-center py-8">
          <Monitor className="w-16 h-16 text-orange-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {t('ai.monitoring.title', 'System Health')}
          </h3>
          <p className="text-gray-600">
            {t('ai.monitoring.description', 'Real-time monitoring of AI system status')}
          </p>
        </div>
      )
    }
  ];

  const quickActions = [
    {
      id: 'start-tutorial',
      title: t('ai.actions.tutorial.title', 'Start AI Tutorial'),
      description: t('ai.actions.tutorial.description', 'Learn AI features step by step'),
      icon: <BookOpen className="w-6 h-6" />,
      action: () => setShowTutorials(true),
      color: 'bg-blue-500'
    },
    {
      id: 'mobile-ai',
      title: t('ai.actions.mobile.title', 'Mobile AI Assistant'),
      description: t('ai.actions.mobile.description', 'Access AI on mobile devices'),
      icon: <Smartphone className="w-6 h-6" />,
      action: () => setShowMobileInterface(true),
      color: 'bg-green-500'
    },
    {
      id: 'ai-onboarding',
      title: t('ai.actions.tour.title', 'AI Platform Tour'),
      description: t('ai.actions.tour.description', 'Discover AI capabilities'),
      icon: <Star className="w-6 h-6" />,
      action: () => setShowOnboarding(true),
      color: 'bg-purple-500'
    }
  ];

  // Show loading state
  if (isStatusLoading) {
    return (
      <AuthenticatedLayout>
        <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="max-w-7xl mx-auto w-full flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <RefreshCw className="w-12 h-12 text-white animate-spin mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-white mb-2">
                  {t('ai.loading.title', 'Loading AI Dashboard')}
                </h2>
                <p className="text-gray-300">
                  {t('ai.loading.message', 'Connecting to AI services...')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  // Show error state if there's a critical error
  if (statusError && !isStatusLoading) {
    return (
      <div className={`min-h-screen bg-gradient-to-b from-red-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <h2 className="text-lg font-semibold mb-2">
                {t('ai.error.title', 'AI Service Error')}
              </h2>
              <p>{t('ai.error.message', 'Unable to connect to AI services. Please try again later.')}</p>
              <p className="text-sm mt-2">Error: {statusError}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full space-y-6">
          {/* Header */}
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-2">
                  {t('ai.dashboard.title', 'AI Command Center')}
                </h1>
                <p className="text-blue-100 text-lg">
                  {t('ai.dashboard.subtitle', 'Your comprehensive AI management and monitoring dashboard')}
                </p>
              </div>
              <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
                <div className={`bg-white bg-opacity-20 rounded-lg p-3 ${
                  realTimeStats?.isOnline ? '' : 'animate-pulse'
                }`}>
                  <Activity className={`w-8 h-8 ${
                    realTimeStats?.isOnline ? 'text-white' : 'text-red-300'
                  }`} />
                </div>
                <div className="text-right">
                  <p className="text-sm text-blue-100">{t('ai.status.label', 'AI Status')}</p>
                  <p className="text-lg font-semibold">
                    {aiLoading ? t('common.loading', 'Loading...') :
                     realTimeStats?.isOnline ? t('ai.status.online', 'Online') : t('ai.status.offline', 'Offline')}
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-8">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                <div className="flex items-center">
                  <Brain className="w-6 h-6 mr-3" />
                  <div>
                    <p className="text-sm text-blue-100">{t('ai.stats.workers', 'AI Workers')}</p>
                    <p className="text-xl font-bold">
                      {realTimeStats?.status?.ai_workers_running || 0} {t('ai.stats.running', 'Running')}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                <div className="flex items-center">
                  <TrendingUp className="w-6 h-6 mr-3" />
                  <div>
                    <p className="text-sm text-gray-300">{t('ai.stats.ideas_enhanced', 'Ideas Enhanced')}</p>
                    <p className="text-xl font-bold">
                      {stats?.ideas_enhanced || 0}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                <div className="flex items-center">
                  <Users className="w-6 h-6 mr-3" />
                  <div>
                    <p className="text-sm text-gray-300">{t('ai.stats.users_helped', 'Users Helped')}</p>
                    <p className="text-xl font-bold">
                      {realTimeStats?.impact?.users_helped || 0}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                <div className="flex items-center">
                  <Zap className="w-6 h-6 mr-3" />
                  <div>
                    <p className="text-sm text-gray-300">{t('ai.stats.actions_today', 'Actions Today')}</p>
                    <p className="text-xl font-bold">
                      {stats?.total_actions_today || 0}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Status Panel */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {t('ai.system.status.title', 'AI System Status')}
                </h3>
                <p className="text-gray-300">
                  {t('ai.system.status.description', 'AI services are {{status}}', {
                    status: isAIRunning ? t('ai.status.running', 'running') : t('ai.status.offline', 'offline')
                  })}
                </p>
              </div>
              <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                isAIRunning
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {isAIRunning ? t('ai.status.online', 'Online') : t('ai.status.offline', 'Offline')}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickActions.map((action) => (
              <button
                key={action.id}
                onClick={action.action}
                className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20 hover:border-purple-500/50 transition-all text-left group"
              >
                <div className="flex items-center mb-3">
                  <div className={`p-3 ${action.color} text-white rounded-lg mr-4`}>
                    {action.icon}
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors ml-auto" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {action.title}
                </h3>
                <p className="text-gray-300">
                  {action.description}
                </p>
              </button>
            ))}
          </div>

          {/* Navigation Tabs */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            <div className="border-b border-white/20">
              <nav className="flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-400'
                        : 'border-transparent text-gray-400 hover:text-white hover:border-white/30'
                    }`}
                  >
                    {tab.icon}
                    <span className="ml-2">{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {tabs.find(tab => tab.id === activeTab)?.component}
            </div>
          </div>

          {/* AI Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Feature Cards */}
            <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-blue-600 rounded-lg mr-4">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{t('ai.features.assistant.title', 'Smart Assistant')}</h3>
                  <p className="text-sm text-gray-300">{t('ai.features.assistant.description', 'Contextual AI help')}</p>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">{t('ai.features.status', 'Status')}:</span>
                  <span className={`font-medium ${isAIRunning ? 'text-green-400' : 'text-red-400'}`}>
                    {isAIRunning ? t('ai.status.active', 'Active') : t('ai.status.offline', 'Offline')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">{t('ai.features.performance', 'Performance')}:</span>
                  <span className="font-medium text-white">{performanceStatus?.status || t('common.unknown', 'Unknown')}</span>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-green-600 rounded-lg mr-4">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{t('ai.features.analysis.title', 'Business Analysis')}</h3>
                  <p className="text-sm text-gray-300">{t('ai.features.analysis.description', 'AI-powered insights')}</p>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">{t('ai.stats.ideas_enhanced', 'Ideas Enhanced')}:</span>
                  <span className="font-medium text-white">{stats?.ideas_enhanced || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">{t('ai.stats.opportunities', 'Opportunities')}:</span>
                  <span className="font-medium text-green-400">{stats?.opportunities_found || 0}</span>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-purple-600 rounded-lg mr-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{t('ai.features.predictive.title', 'Predictive Intelligence')}</h3>
                  <p className="text-sm text-gray-300">{t('ai.features.predictive.description', 'Future insights')}</p>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">{t('ai.stats.actions_today', 'Actions Today')}:</span>
                  <span className="font-medium text-white">{stats?.total_actions_today || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">{t('ai.stats.workers', 'Workers')}:</span>
                  <span className="font-medium text-green-400">
                    {realTimeStats?.status?.ai_workers_running || 0} {t('ai.stats.running', 'Running')}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Help Section */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <HelpCircle className="w-6 h-6 text-blue-400 mr-3" />
              <h3 className="text-lg font-semibold text-white">{t('ai.help.title', 'Need Help?')}</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => setShowTutorials(true)}
                className="bg-white/20 p-4 rounded-lg border border-white/30 hover:border-purple-500/50 transition-all text-left"
              >
                <BookOpen className="w-5 h-5 text-blue-400 mb-2" />
                <h4 className="font-medium text-white">{t('ai.help.tutorials.title', 'Interactive Tutorials')}</h4>
                <p className="text-sm text-gray-300">{t('ai.help.tutorials.description', 'Learn AI features step by step')}</p>
              </button>
              <button
                onClick={() => setShowOnboarding(true)}
                className="bg-white/20 p-4 rounded-lg border border-white/30 hover:border-purple-500/50 transition-all text-left"
              >
                <Star className="w-5 h-5 text-purple-400 mb-2" />
                <h4 className="font-medium text-white">{t('ai.help.tour.title', 'Platform Tour')}</h4>
                <p className="text-sm text-gray-300">{t('ai.help.tour.description', 'Discover all AI capabilities')}</p>
              </button>
              <div className="bg-white/20 p-4 rounded-lg border border-white/30">
                <Shield className="w-5 h-5 text-green-400 mb-2" />
                <h4 className="font-medium text-white">{t('ai.help.support.title', '24/7 Support')}</h4>
                <p className="text-sm text-gray-300">{t('ai.help.support.description', 'Get help when you need it')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Simple Modal Placeholders */}
        {showOnboarding && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-md mx-4 border border-white/20">
              <h3 className="text-lg font-semibold mb-4 text-white">
                {t('ai.modals.tour.title', 'AI Platform Tour')}
              </h3>
              <p className="text-gray-300 mb-4">
                {t('ai.modals.tour.message', 'Welcome to the AI platform! This feature is coming soon.')}
              </p>
              <button
                onClick={() => setShowOnboarding(false)}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
              >
                {t('common.close', 'Close')}
              </button>
            </div>
          </div>
        )}

        {showTutorials && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-md mx-4 border border-white/20">
              <h3 className="text-lg font-semibold mb-4 text-white">
                {t('ai.modals.tutorials.title', 'AI Tutorials')}
              </h3>
              <p className="text-gray-300 mb-4">
                {t('ai.modals.tutorials.message', 'Interactive tutorials are coming soon!')}
              </p>
              <button
                onClick={() => setShowTutorials(false)}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
              >
                {t('common.close', 'Close')}
              </button>
            </div>
          </div>
        )}

        {showMobileInterface && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-md mx-4 border border-white/20">
              <h3 className="text-lg font-semibold mb-4 text-white">
                {t('ai.modals.mobile.title', 'Mobile AI Interface')}
              </h3>
              <p className="text-gray-300 mb-4">
                {t('ai.modals.mobile.message', 'Mobile interface is coming soon!')}
              </p>
              <button
                onClick={() => setShowMobileInterface(false)}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
              >
                {t('common.close', 'Close')}
              </button>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

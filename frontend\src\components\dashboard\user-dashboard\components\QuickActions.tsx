import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../../store/hooks';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText, RTLFlex, RTLIcon } from '../../../common';
import { Lightbulb, Users, BookOpen, DollarSign, ArrowRight, FileText, Brain, Sparkles } from 'lucide-react';
import { getUserRole, canAccessRoute } from '../../../../utils/unifiedRoleManager';
import { canAccessNavItem } from '../../../../config/navigationConfig';
const QuickActions: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);
  const userRole = getUserRole(user);

  // Check if user can access specific navigation items
  const canAccessTemplates = canAccessNavItem(userRole, 'templates');
  const canAccessFunding = canAccessNavItem(userRole, 'funding'); // This will be false since funding is not in config

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
      <RTLText as="h2" className="text-xl font-semibold mb-6">{t('dashboard.quickActions.title')}</RTLText>

      {/* Featured AI Actions Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
        <Link
          to="/dashboard/business-ideas/new"
          className="bg-gradient-to-r from-blue-600/80 to-purple-600/80 hover:from-blue-700/80 hover:to-purple-700/80 rounded-lg p-4 border border-blue-500/30 transition-all duration-300 group"
        >
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <div className="relative">
              <Brain className="w-6 h-6 text-white" />
              <Sparkles className="w-3 h-3 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold text-white mb-1">
                {t('dashboard.aiEnhancedIdea', '🚀 AI-Enhanced Business Idea Creator')}
              </h3>
              <p className="text-blue-100 text-xs">
                {t('dashboard.aiEnhancedDescription', 'Create business ideas with real-time AI assistance')}
              </p>
            </div>
            <ArrowRight className="w-4 h-4 text-white group-hover:translate-x-1 transition-transform" />
          </div>
        </Link>

        <Link
          to="/chat/enhanced"
          className="bg-gradient-to-r from-purple-600/80 to-blue-600/80 hover:from-purple-700/80 hover:to-blue-700/80 rounded-lg p-4 border border-purple-500/30 transition-all duration-300 group"
        >
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <div className="relative">
              <Brain className="w-6 h-6 text-white" />
              <Sparkles className="w-3 h-3 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold text-white mb-1">
                {t('dashboard.aiWorkflows', '⚡ AI Workflows Dashboard')}
              </h3>
              <p className="text-purple-100 text-xs">
                {t('dashboard.aiWorkflowsDescription', 'Automated business plan generation')}
              </p>
            </div>
            <ArrowRight className="w-4 h-4 text-white group-hover:translate-x-1 transition-transform" />
          </div>
        </Link>

        <Link
          to="/dashboard/predictive-intelligence"
          className="bg-gradient-to-r from-indigo-600/80 to-purple-600/80 hover:from-indigo-700/80 hover:to-purple-700/80 rounded-lg p-4 border border-indigo-500/30 transition-all duration-300 group"
        >
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <div className="relative">
              <Brain className="w-6 h-6 text-white" />
              <Sparkles className="w-3 h-3 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold text-white mb-1">
                {t('dashboard.predictiveIntelligence', '🔮 Predictive Intelligence Dashboard')}
              </h3>
              <p className="text-indigo-100 text-xs">
                {t('dashboard.predictiveDescription', 'Success predictions and risk assessment')}
              </p>
            </div>
            <ArrowRight className="w-4 h-4 text-white group-hover:translate-x-1 transition-transform" />
          </div>
        </Link>


      </div>

      {/* Regular Quick Actions */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
        <Link
          to="/dashboard/business-ideas/new"
          className={`bg-purple-900/40 hover:bg-purple-800/50 backdrop-blur-sm rounded-lg p-3 border border-purple-800/50 flex flex-col items-center text-center transition-all duration-300 group ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <RTLIcon icon={Lightbulb} size={24} className="text-purple-400 mb-2 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">{t('dashboard.submitNewIdea')}</span>
        </Link>

        <Link
          to="/dashboard/mentorship/apply"
          className={`bg-blue-900/40 hover:bg-blue-800/50 backdrop-blur-sm rounded-lg p-3 border border-blue-800/50 flex flex-col items-center text-center transition-all duration-300 group ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <RTLIcon icon={Users} size={24} className="text-blue-400 mb-2 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">{t('dashboard.findMentor')}</span>
        </Link>

        <Link
          to="/dashboard/resources"
          className={`bg-green-900/40 hover:bg-green-800/50 backdrop-blur-sm rounded-lg p-3 border border-green-800/50 flex flex-col items-center text-center transition-all duration-300 group ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <RTLIcon icon={BookOpen} size={24} className="text-green-400 mb-2 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">{t('dashboard.exploreResources')}</span>
        </Link>

        <Link
          to="/dashboard/funding"
          className={`bg-amber-900/40 hover:bg-amber-800/50 backdrop-blur-sm rounded-lg p-3 border border-amber-800/50 flex flex-col items-center text-center transition-all duration-300 group ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <RTLIcon icon={DollarSign} size={24} className="text-amber-400 mb-2 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">{t('dashboard.exploreFunding')}</span>
        </Link>

        <Link
          to="/dashboard/business-plans"
          className={`bg-violet-900/40 hover:bg-violet-800/50 backdrop-blur-sm rounded-lg p-3 border border-violet-800/50 flex flex-col items-center text-center transition-all duration-300 group ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <RTLIcon icon={FileText} size={24} className="text-violet-400 mb-2 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">{t('dashboard.createBusinessPlan')}</span>
        </Link>
      </div>
    </div>
  );
};

export default QuickActions;
